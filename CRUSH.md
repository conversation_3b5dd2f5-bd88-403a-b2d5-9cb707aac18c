
# Crush Operations

This document outlines the commands and conventions for development within this repository.

## ⚙️ Commands

- **Run all tests:** `pytest`
- **Run a single test file:** `pytest tests/path/to/test_file.py`
- **Frontend dev server:** `npm --prefix frontend run dev`
- **Frontend build:** `npm --prefix frontend run build`
- **Frontend lint:** `npm --prefix frontend run lint`

## ✍️ Code Style

### General Principles
- **Configuration-driven:** All mutable values must be managed through the unified configuration system (`get_unified_config()`). Hardcoding is strictly prohibited.
- **DRY (Don't Repeat Yourself):** Avoid code duplication. Extract common logic into reusable functions or classes.
- **Single Responsibility:** Each function and class should have a single, well-defined purpose.
- **Dependency Injection:** Inject dependencies rather than creating them directly within a class or function.

### Python
- **Imports:** Use precise, absolute imports. Avoid wildcard imports (`from module import *`). Unused imports should be removed.
- **Naming:** Configuration keys should follow a hierarchical structure (e.g., `thresholds.confidence.default`).
- **Error Handling:** Use configuration for error messages instead of hardcoded strings. Implement fallback mechanisms for configuration access.
- **Strings:** All user-facing strings, especially Chinese characters, must be managed through configuration templates, not hardcoded.

### Frontend (TypeScript/React)
- **Formatting:** Follow standard Prettier and ESLint rules.
- **Components:** Keep components small and focused on a single piece of functionality.
- **Styling:** Use Tailwind CSS for styling, adhering to the project's design system.
