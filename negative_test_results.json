{"basic_tests": {"total_cases": 13, "passed": 3, "failed": 10, "test_results": [{"name": "yaml_invalid_indentation", "description": "YAML缩进错误", "success": false, "message": "期望YAML解析失败，但实际成功", "error_message": "", "expected_error_type": "YAMLError", "expected_keywords": ["缩进", "indentation", "语法错误"]}, {"name": "yaml_invalid_syntax", "description": "YAML语法错误 - 冒号缺失", "success": true, "message": "YAML解析失败（符合预期）", "error_message": "expected '<document start>', but found '<block mapping start>'\n  in \"<unicode string>\", line 3, column 1:\n    models:\n    ^", "expected_error_type": "YAMLError", "expected_keywords": ["语法错误", "冒号", "syntax"]}, {"name": "missing_required_field", "description": "缺少必需字段", "success": false, "message": "错误信息缺少关键词: ['缺少', '字段']", "error_message": "2 validation errors for LLMConfigSchema\ndefault_model\n  Field required [type=missing, input_value={'models': {'test-model': {'provider': 'test'}}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nmodels.test-model.model_name\n  Field required [type=missing, input_value={'provider': 'test'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing", "expected_error_type": "ValidationError", "expected_keywords": ["缺少", "字段", "model_name"]}, {"name": "invalid_field_type", "description": "字段类型错误", "success": false, "message": "错误信息缺少关键词: ['类型错误', '数字']", "error_message": "1 validation error for LLMConfigSchema\nmodels.test-model.temperature\n  Input should be a valid number, unable to parse string as a number [type=float_parsing, input_value='invalid_number', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/float_parsing", "expected_error_type": "ValidationError", "expected_keywords": ["类型错误", "数字", "invalid_number"]}, {"name": "empty_string_field", "description": "空字符串字段", "success": false, "message": "错误信息缺少关键词: ['长度不足', '字符', '空字符串']", "error_message": "1 validation error for LLMConfigSchema\ndefault_model\n  String should have at least 1 character [type=string_too_short, input_value='', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/string_too_short", "expected_error_type": "ValidationError", "expected_keywords": ["长度不足", "字符", "空字符串"]}, {"name": "nonexistent_default_model", "description": "默认模型不存在", "success": false, "message": "错误信息缺少关键词: ['不存在', '未定义']", "error_message": "1 validation error for LLMConfigSchema\n  Value error, 默认模型 'nonexistent-model' 未在模型配置中定义 [type=value_error, input_value={'default_model': 'nonexi...me': 'existing-model'}}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error", "expected_error_type": "ValidationError", "expected_keywords": ["默认模型", "不存在", "未定义", "nonexistent-model"]}, {"name": "invalid_scenario_mapping", "description": "场景映射到不存在的模型", "success": true, "message": "测试通过", "error_message": "1 validation error for LLMConfigSchema\nscenario_mapping\n  Value error, 场景 'test_scenario' 映射的模型 'nonexistent-model' 不存在 [type=value_error, input_value={'test_scenario': 'nonexistent-model'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error", "expected_error_type": "ValidationError", "expected_keywords": ["场景", "映射", "模型", "不存在", "nonexistent-model"]}, {"name": "boolean_as_string", "description": "布尔值写成字符串", "success": false, "message": "期望Schema验证失败，但实际成功", "error_message": "", "expected_error_type": "ValidationError", "expected_keywords": ["布尔", "boolean", "true", "字符串"]}, {"name": "number_as_string", "description": "数字写成字符串", "success": false, "message": "期望Schema验证失败，但实际成功", "error_message": "", "expected_error_type": "ValidationError", "expected_keywords": ["数字", "integer", "字符串", "30"]}, {"name": "temperature_out_of_range", "description": "温度参数超出范围", "success": false, "message": "错误信息缺少关键词: ['温度', '范围', '2.0']", "error_message": "1 validation error for LLMConfigSchema\nmodels.test-model.temperature\n  Input should be less than or equal to 2 [type=less_than_equal, input_value=3.0, input_type=float]\n    For further information visit https://errors.pydantic.dev/2.11/v/less_than_equal", "expected_error_type": "ValidationError", "expected_keywords": ["温度", "temperature", "范围", "3.0", "2.0"]}, {"name": "negative_timeout", "description": "负数超时时间", "success": false, "message": "错误信息缺少关键词: ['超时', '负数', '大于等于', 'ge']", "error_message": "1 validation error for PerformanceConfigSchema\ntimeout.default\n  Input should be greater than or equal to 1 [type=greater_than_equal, input_value=-10, input_type=int]\n    For further information visit https://errors.pydantic.dev/2.11/v/greater_than_equal", "expected_error_type": "ValidationError", "expected_keywords": ["超时", "timeout", "负数", "大于等于", "ge"]}, {"name": "database_path_invalid_extension", "description": "数据库文件扩展名错误", "success": false, "message": "期望Schema验证失败，但实际成功", "error_message": "", "expected_error_type": "ValidationError", "expected_keywords": ["数据库", "扩展名", ".db", ".txt"]}, {"name": "invalid_api_base_url", "description": "无效的API基础URL", "success": true, "message": "测试通过", "error_message": "1 validation error for LLMConfigSchema\nmodels.test-model.api_base\n  Value error, API基础URL必须以http://或https://开头 [type=value_error, input_value='invalid-url', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error", "expected_error_type": "ValidationError", "expected_keywords": ["URL", "http", "https", "invalid-url"]}]}, "formatting_tests": {"total_cases": 11, "readable_errors": 11, "formatting_tests": [{"test_case": "missing_required_field", "formatted_error": "🚨 配置验证失败\n==================================================\n📁 文件: missing_required_field.yaml\n❌ 发现 2 个错误\n\n错误 1: 缺少必需字段 'default_model'\n   📍 位置: default_model\n   🔍 输入值: {'models': {'test-model': {'provider': 'test'}}}\n   💡 解决方案:\n      • 在配置文件中添加缺少的字段\n      • 检查字段名是否拼写正确\n      • 参考配置模板或文档中的示例\n   📝 示例:\n      model_name: model_name: 'your-model-name'\n      provider: provider: 'openai'\n      default_model: default_model: 'gpt-3.5-turbo'\n\n错误 2: 缺少必需字段 'model_name'\n   📍 位置: models -> test-model -> model_name\n   🔍 输入值: {'provider': 'test'}\n   💡 解决方案:\n      • 在配置文件中添加缺少的字段\n      • 检查字段名是否拼写正确\n      • 参考配置模板或文档中的示例\n   📝 示例:\n      model_name: model_name: 'your-model-name'\n      provider: provider: 'openai'\n      default_model: default_model: 'gpt-3.5-turbo'\n\n📚 更多帮助:\n   • 查看配置文档: docs/development/配置管理指南.md\n   • 运行配置验证: python tools/config_validation_cli.py\n   • 查看配置示例: backend/config/defaults/\n", "readability_score": 1.0, "is_readable": true}, {"test_case": "invalid_field_type", "formatted_error": "🚨 配置验证失败\n==================================================\n📁 文件: invalid_field_type.yaml\n❌ 发现 1 个错误\n\n错误 1: 字段类型错误：期望数字类型，实际输入 'invalid_number'\n   📍 位置: models -> test-model -> temperature\n   🔍 输入值: invalid_number\n   💡 解决方案:\n      • 移除数字值周围的引号\n      • 确保使用正确的数字格式\n      • 检查是否误用了字符串\n   📝 示例:\n      integer: timeout: 30  # 不是 '30'\n      float: temperature: 0.7  # 不是 '0.7'\n\n📚 更多帮助:\n   • 查看配置文档: docs/development/配置管理指南.md\n   • 运行配置验证: python tools/config_validation_cli.py\n   • 查看配置示例: backend/config/defaults/\n", "readability_score": 1.0, "is_readable": true}, {"test_case": "empty_string_field", "formatted_error": "🚨 配置验证失败\n==================================================\n📁 文件: empty_string_field.yaml\n❌ 发现 1 个错误\n\n错误 1: 字符串长度不足：至少需要 1 个字符，当前为空字符串\n   📍 位置: default_model\n   🔍 输入值: 未提供\n   💡 解决方案:\n      • 提供非空的字符串值\n      • 检查是否误用了空字符串\n      • 确保字段值有意义且完整\n   📝 示例:\n      non_empty: model_name: 'gpt-3.5-turbo'  # 不能为空\n\n📚 更多帮助:\n   • 查看配置文档: docs/development/配置管理指南.md\n   • 运行配置验证: python tools/config_validation_cli.py\n   • 查看配置示例: backend/config/defaults/\n", "readability_score": 1.0, "is_readable": true}, {"test_case": "nonexistent_default_model", "formatted_error": "🚨 配置验证失败\n==================================================\n📁 文件: nonexistent_default_model.yaml\n❌ 发现 1 个错误\n\n错误 1: 业务逻辑错误：默认模型 'nonexistent-model' 未在模型配置中定义\n   📍 位置: 根级别\n   🔍 输入值: {'default_model': 'nonexistent-model', 'models': {'existing-model': {'provider': 'test', 'model_name': 'existing-model'}}}\n   💡 解决方案:\n      • 检查业务逻辑约束\n      • 确保引用的资源存在\n      • 验证配置项之间的依赖关系\n\n📚 更多帮助:\n   • 查看配置文档: docs/development/配置管理指南.md\n   • 运行配置验证: python tools/config_validation_cli.py\n   • 查看配置示例: backend/config/defaults/\n", "readability_score": 0.85, "is_readable": true}, {"test_case": "invalid_scenario_mapping", "formatted_error": "🚨 配置验证失败\n==================================================\n📁 文件: invalid_scenario_mapping.yaml\n❌ 发现 1 个错误\n\n错误 1: 业务逻辑错误：场景 'test_scenario' 映射的模型 'nonexistent-model' 不存在\n   📍 位置: scenario_mapping\n   🔍 输入值: {'test_scenario': 'nonexistent-model'}\n   💡 解决方案:\n      • 检查业务逻辑约束\n      • 确保引用的资源存在\n      • 验证配置项之间的依赖关系\n\n📚 更多帮助:\n   • 查看配置文档: docs/development/配置管理指南.md\n   • 运行配置验证: python tools/config_validation_cli.py\n   • 查看配置示例: backend/config/defaults/\n", "readability_score": 1.0, "is_readable": true}, {"test_case": "boolean_as_string", "formatted_error": "🚨 配置验证失败\n==================================================\n📁 文件: boolean_as_string.yaml\n❌ 发现 1 个错误\n\n错误 1: 缺少必需字段 'default_model'\n   📍 位置: default_model\n   🔍 输入值: {'cache': {'enabled': 'true'}}\n   💡 解决方案:\n      • 在配置文件中添加缺少的字段\n      • 检查字段名是否拼写正确\n      • 参考配置模板或文档中的示例\n   📝 示例:\n      model_name: model_name: 'your-model-name'\n      provider: provider: 'openai'\n      default_model: default_model: 'gpt-3.5-turbo'\n\n📚 更多帮助:\n   • 查看配置文档: docs/development/配置管理指南.md\n   • 运行配置验证: python tools/config_validation_cli.py\n   • 查看配置示例: backend/config/defaults/\n", "readability_score": 0.85, "is_readable": true}, {"test_case": "number_as_string", "formatted_error": "🚨 配置验证失败\n==================================================\n📁 文件: number_as_string.yaml\n❌ 发现 1 个错误\n\n错误 1: 缺少必需字段 'default_model'\n   📍 位置: default_model\n   🔍 输入值: {'timeout': {'default': '30'}}\n   💡 解决方案:\n      • 在配置文件中添加缺少的字段\n      • 检查字段名是否拼写正确\n      • 参考配置模板或文档中的示例\n   📝 示例:\n      model_name: model_name: 'your-model-name'\n      provider: provider: 'openai'\n      default_model: default_model: 'gpt-3.5-turbo'\n\n📚 更多帮助:\n   • 查看配置文档: docs/development/配置管理指南.md\n   • 运行配置验证: python tools/config_validation_cli.py\n   • 查看配置示例: backend/config/defaults/\n", "readability_score": 0.7749999999999999, "is_readable": true}, {"test_case": "temperature_out_of_range", "formatted_error": "🚨 配置验证失败\n==================================================\n📁 文件: temperature_out_of_range.yaml\n❌ 发现 1 个错误\n\n错误 1: 数值过大：必须小于等于 2，当前值为 3.0\n   📍 位置: models -> test-model -> temperature\n   🔍 输入值: 3.0\n   💡 解决方案:\n      • 减小数值到允许的最大值以下\n      • 检查配置文档中的取值范围\n      • 考虑使用推荐的默认值\n   📝 示例:\n      within_range: temperature: 1.0  # 不能超过2.0\n\n📚 更多帮助:\n   • 查看配置文档: docs/development/配置管理指南.md\n   • 运行配置验证: python tools/config_validation_cli.py\n   • 查看配置示例: backend/config/defaults/\n", "readability_score": 0.94, "is_readable": true}, {"test_case": "negative_timeout", "formatted_error": "🚨 配置验证失败\n==================================================\n📁 文件: negative_timeout.yaml\n❌ 发现 1 个错误\n\n错误 1: 缺少必需字段 'default_model'\n   📍 位置: default_model\n   🔍 输入值: {'timeout': {'default': -10}}\n   💡 解决方案:\n      • 在配置文件中添加缺少的字段\n      • 检查字段名是否拼写正确\n      • 参考配置模板或文档中的示例\n   📝 示例:\n      model_name: model_name: 'your-model-name'\n      provider: provider: 'openai'\n      default_model: default_model: 'gpt-3.5-turbo'\n\n📚 更多帮助:\n   • 查看配置文档: docs/development/配置管理指南.md\n   • 运行配置验证: python tools/config_validation_cli.py\n   • 查看配置示例: backend/config/defaults/\n", "readability_score": 0.76, "is_readable": true}, {"test_case": "database_path_invalid_extension", "formatted_error": "🚨 配置验证失败\n==================================================\n📁 文件: database_path_invalid_extension.yaml\n❌ 发现 1 个错误\n\n错误 1: 缺少必需字段 'default_model'\n   📍 位置: default_model\n   🔍 输入值: {'database': {'connection': {'path': 'database.txt', 'timeout': 30}}}\n   💡 解决方案:\n      • 在配置文件中添加缺少的字段\n      • 检查字段名是否拼写正确\n      • 参考配置模板或文档中的示例\n   📝 示例:\n      model_name: model_name: 'your-model-name'\n      provider: provider: 'openai'\n      default_model: default_model: 'gpt-3.5-turbo'\n\n📚 更多帮助:\n   • 查看配置文档: docs/development/配置管理指南.md\n   • 运行配置验证: python tools/config_validation_cli.py\n   • 查看配置示例: backend/config/defaults/\n", "readability_score": 0.7749999999999999, "is_readable": true}, {"test_case": "invalid_api_base_url", "formatted_error": "🚨 配置验证失败\n==================================================\n📁 文件: invalid_api_base_url.yaml\n❌ 发现 1 个错误\n\n错误 1: 业务逻辑错误：API基础URL必须以http://或https://开头\n   📍 位置: models -> test-model -> api_base\n   🔍 输入值: invalid-url\n   💡 解决方案:\n      • 检查业务逻辑约束\n      • 确保引用的资源存在\n      • 验证配置项之间的依赖关系\n\n📚 更多帮助:\n   • 查看配置文档: docs/development/配置管理指南.md\n   • 运行配置验证: python tools/config_validation_cli.py\n   • 查看配置示例: backend/config/defaults/\n", "readability_score": 1.0, "is_readable": true}]}}