{"timestamp": "2025-08-18T11:07:03.159727", "summary": {"total_tests": 9, "passed_tests": 5, "failed_tests": 4, "success_rate": 0.5555555555555556, "total_duration": 1.5418000221252441}, "test_results": [{"test_name": "统一配置加载器测试", "success": false, "duration": 0.22548198699951172, "error": ""}, {"test_name": "统一状态管理器测试", "success": false, "duration": 0.6999928951263428, "error": "'DOMAIN_CLARIFICATION' is not a valid ConversationState"}, {"test_name": "对话状态机测试", "success": true, "duration": 0.00269317626953125, "error": ""}, {"test_name": "简化决策引擎测试", "success": false, "duration": 0.5695350170135498, "error": ""}, {"test_name": "统一LLM客户端工厂测试", "success": false, "duration": 0.0012059211730957031, "error": ""}, {"test_name": "端到端对话流程测试", "success": true, "duration": 0.00102996826171875, "error": ""}, {"test_name": "并发处理测试", "success": true, "duration": 0.0018999576568603516, "error": ""}, {"test_name": "错误处理测试", "success": true, "duration": 0.0005059242248535156, "error": ""}, {"test_name": "性能基准测试", "success": true, "duration": 0.03945517539978027, "error": ""}], "detailed_results": [{"success": false, "checks": {"system_config_loaded": true, "llm_types_available": false, "conversation_config_loaded": true, "keyword_rules_loaded": true, "message_templates_loaded": true}, "llm_types_count": 1, "system_version": "3.0"}, {"success": false, "error": "'DOMAIN_CLARIFICATION' is not a valid ConversationState"}, {"success": true, "sequence_results": [{"message": "你好", "expected_state": "IDLE", "actual_state": "IDLE", "success": true, "state_match": true}, {"message": "我想做一个网站", "expected_state": "COLLECTING_INFO", "actual_state": "COLLECTING_INFO", "success": true, "state_match": true}, {"message": "这是一个电商网站", "expected_state": "COLLECTING_INFO", "actual_state": "COLLECTING_INFO", "success": true, "state_match": true}, {"message": "确认", "expected_state": "DOCUMENTING", "actual_state": "DOCUMENTING", "success": true, "state_match": true}, {"message": "好的", "expected_state": "COMPLETED", "actual_state": "COMPLETED", "success": true, "state_match": true}], "success_rate": 1.0, "total_states": 4}, {"success": false, "validation": {"valid": true, "issues": [], "statistics": {"total_states": 5, "total_rules": 32, "states_with_default": 5, "intent_priority_rules": 13, "priority_order": ["search_knowledge_base", "ask_question", "business_requirement", "ask_introduction", "ask_capabilities", "modify", "confirm", "restart", "emotional_support", "greeting", "general_chat", "provide_information", "unknown"]}}, "decision_results": [{"intent": "greeting", "state": "IDLE", "expected_action": "send_greeting", "actual_action": "send_greeting", "match": true}, {"intent": "business_requirement", "state": "IDLE", "expected_action": "start_requirement_collection", "actual_action": "start_requirement_collection", "match": true}, {"intent": "provide_information", "state": "COLLECTING_INFO", "expected_action": "collect_information", "actual_action": "process_answer_and_ask_next", "match": false}, {"intent": "confirm", "state": "COLLECTING_INFO", "expected_action": "start_document_generation", "actual_action": "start_document_generation", "match": true}, {"intent": "confirm", "state": "DOCUMENTING", "expected_action": "confirm_document", "actual_action": "confirm_document", "match": true}], "success_rate": 0.8, "available_actions": 10}, {"success": false, "available_types": ["default", "domain_classification", "category_classification", "intent_recognition", "information_extraction", "question_optimization", "document_generation", "conversation"], "creation_results": [{"type": "default", "success": false, "error": "The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable"}, {"type": "domain_classification", "success": false, "error": "The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable"}, {"type": "conversation", "success": false, "error": "The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable"}], "success_rate": 0.0}, {"success": true, "flow_results": [{"step": 1, "message": "你好", "success": true, "state": "IDLE", "response_available": true}, {"step": 2, "message": "我想做一个电商网站", "success": true, "state": "COLLECTING_INFO", "response_available": true}, {"step": 3, "message": "主要卖服装和配饰", "success": true, "state": "COLLECTING_INFO", "response_available": true}, {"step": 4, "message": "需要支付功能", "success": true, "state": "COLLECTING_INFO", "response_available": true}, {"step": 5, "message": "确认", "success": true, "state": "DOCUMENTING", "response_available": true}, {"step": 6, "message": "好的", "success": true, "state": "COMPLETED", "response_available": true}], "success_rate": 1.0, "final_state": "COMPLETED"}, {"success": true, "concurrent_sessions": 10, "success_count": 10, "success_rate": 1.0}, {"success": true, "error_tests": [{"test": "empty_message", "handled": true}, {"test": "unknown_intent", "handled": true}, {"test": "long_message", "handled": true}], "handled_rate": 1.0}, {"success": true, "performance_results": {"decision_engine_1000_calls": 0.03748297691345215, "state_machine_100_calls": 0.001965045928955078}, "benchmarks": {"decision_avg_ms": 0.03748297691345215, "state_machine_avg_ms": 0.01965045928955078, "decision_meets_target": true, "state_machine_meets_target": true}}]}