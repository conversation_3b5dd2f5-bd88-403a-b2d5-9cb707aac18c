# 配置一致性检查报告

⚠️ 发现 1756 个配置一致性问题

## 🚫 硬编码配置值

- **backend/tasks/config_monitoring_scheduler.py:60**
  ```python
  thread_timeout = 5
  ```

- **backend/agents/agent_instance_pool.py:375**
  ```python
  thread_timeout = 5
  ```

- **backend/agents/conversation_flow/core_refactored.py:1116**
  ```python
  temperature=0.7,
  ```

- **backend/agents/conversation_flow/core_refactored.py:1117**
  ```python
  max_tokens=300
  ```

- **backend/agents/conversation_flow/core_refactored.py:2268**
  ```python
  temperature = 0.8
  ```

- **backend/utils/performance_monitor.py:481**
  ```python
  thread_timeout = 2
  ```

- **backend/api/main.py:494**
  ```python
  api_timeout = 30.0
  ```

## 🔄 重复配置调用

- **backend/config/service.py**: 6 次配置调用
  - 第20行: `llm_config = config_service.get_llm_config("intent_recognition")`
  - 第23行: `retry_limit = config_service.get_business_rule("retry.max_pending_attempts", 3)`
  - 第26行: `template = config_service.get_message_template("greeting.basic")`
  - 第29行: `threshold = config_service.get_threshold("business_rules.requirement_collection.completion_threshold", 0.7)`
  - 第32行: `config_service.add_change_listener("keyword_config", my_callback)`

- **backend/config/__init__.py**: 5 次配置调用
  - 第17行: `llm_config = config_service.get_llm_config("intent_recognition")`
  - 第18行: `business_rule = config_service.get_business_rule("retry.max_pending_attempts", 3)`
  - 第19行: `template = config_service.get_message_template("greeting.basic")`
  - 第41行: `unified_config_manager = get_unified_config()`
  - 第42行: `config_manager = get_unified_config()`

- **backend/config/settings.py**: 4 次配置调用
  - 第37行: `_config = get_unified_config()`
  - 第148行: `params = config_service.get_scenario_params(scenario)`
  - 第158行: `"get_scenario_params() 已废弃，请使用 config_service.get_scenario_params()",`
  - 第166行: `return config_service.get_scenario_params(scenario)`

- **backend/config/optimization_config.py**: 9 次配置调用
  - 第51行: `return self.config.enable_init_tracking`
  - 第55行: `return self.config.enable_config_cache`
  - 第59行: `return self.config.duplicate_warning_threshold`
  - 第63行: `return count % self.config.stats_report_interval == 0`
  - 第68行: `"enable_init_tracking": self.config.enable_init_tracking,`

- **backend/agents/session_context.py**: 7 次配置调用
  - 第191行: `self.config = get_unified_config()`
  - 第209行: `query = self.config.get_config_value("database.queries.conversations.get_domain_category")`
  - 第245行: `query = self.config.get_config_value("database.queries.messages.get_first_user_message")`
  - 第294行: `query = self.config.get_config_value("database.queries.conversations.update_domain")`
  - 第308行: `query = self.config.get_config_value("database.queries.session_states.insert_or_replace")`

- **backend/agents/unified_llm_client_factory.py**: 4 次配置调用
  - 第67行: `config = get_unified_config()`
  - 第168行: `"model": self.config.get("model", "gpt-3.5-turbo"),`
  - 第169行: `"temperature": self.config.get("temperature", 0.7),`
  - 第170行: `"max_tokens": self.config.get("max_tokens", 1000),`

- **backend/agents/agent_instance_pool.py**: 17 次配置调用
  - 第137行: `unified_config = get_unified_config()`
  - 第172行: `if not self.config.enable_session_cache:`
  - 第184行: `timeout_seconds = self.config.session_timeout_minutes * 60`
  - 第191行: `if self.config.enable_metrics:`
  - 第226行: `if len(self._session_cache) >= self.config.max_cached_sessions:`

- **backend/agents/template_version_manager.py**: 5 次配置调用
  - 第81行: `self.config = get_unified_config()`
  - 第249行: `query = self.config.get_config_value("database.queries.template_versions.get_by_id_version")`
  - 第282行: `update_old_sql = self.config.get_config_value("database.queries.template_versions.update_status_deprecated")`
  - 第289行: `update_new_sql = self.config.get_config_value("database.queries.template_versions.update_status_active")`
  - 第409行: `query = self.config.get_config_value("database.queries.template_versions.get_latest_version")`

- **backend/agents/llm_service.py**: 6 次配置调用
  - 第85行: `"temperature": min(max(kwargs.get("temperature", get_unified_config().get_threshold("confidence.default", 0.7)), 0.0), 2.0),`
  - 第201行: `self.unified_config = get_unified_config()`
  - 第324行: `model_config = self.config_service.get_llm_config_with_metadata(agent_name or model_name or "default")`
  - 第555行: `model_config = self.config_service.get_llm_config_with_metadata(effective_agent_name)`
  - 第620行: `scenario_params = self.config_service.get_scenario_params(scenario) if scenario else {}`

- **backend/agents/message_reply_manager.py**: 4 次配置调用
  - 第101行: `message_config = get_unified_config().get_message_config()`
  - 第144行: `templates_config = self.config.get("templates", {})`
  - 第173行: `generators_config = self.config.get("generators", {})`
  - 第342行: `config = get_unified_config()`

- **backend/agents/review_and_refine.py**: 6 次配置调用
  - 第113行: `return {"success": False, "text_response": get_unified_config().get_config_value("message_templates.error.document_not_found")}`
  - 第124行: `return {"success": False, "text_response": get_unified_config().get_config_value("message_templates.error.processing_failed")}`
  - 第148行: `return {"success": False, "text_response": get_unified_config().get_config_value("message_templates.error.document_save_failed")}`
  - 第163行: `return {"success": False, "text_response": get_unified_config().get_config_value("message_templates.error.internal_error")}`
  - 第277行: `config = config_service.get_llm_config_with_metadata("review_and_refine")`

- **backend/agents/simplified_decision_engine.py**: 17 次配置调用
  - 第68行: `self.config = get_unified_config()`
  - 第105行: `self.use_structured_classification = self.config.get_config_value("intent_classification.use_structured_classification", True)`
  - 第529行: `confidence = self.config.get_threshold("confidence.minimum", 0.0)`
  - 第545行: `confidence = self.config.get_threshold("confidence.high", 0.8)`
  - 第590行: `self.logger.info(self.config.get_message_template("logging.info.intent_recognition_result").format(`

- **backend/agents/conversation_state_machine.py**: 14 次配置调用
  - 第42行: `self.config = get_unified_config()`
  - 第76行: `greeting_keywords = self.config.get_keyword_rules().get('greeting', {}).get('keywords', [])`
  - 第78行: `templates = self.config.get_message_templates().get('greeting', {})`
  - 第79行: `unified_config = get_unified_config()`
  - 第93行: `business_keywords = self.config.get_keyword_rules().get('business_requirement', {}).get('keywords', [])`

- **backend/agents/rag_knowledge_base_agent.py**: 10 次配置调用
  - 第60行: `self.chroma_db_path = self.config.chroma_db.get('path', 'backend/data/chroma_db')`
  - 第61行: `self.collection_name = self.config.chroma_db.get('collection_name', 'hybrid_knowledge_base')`
  - 第62行: `self.embedding_model = self.config.chroma_db.get('embedding_model', 'moka-ai/m3e-base')`
  - 第65行: `self.retrieval_config = self.config.retrieval`
  - 第71行: `self.safety_config = self.config.safety`

- **backend/agents/unified_state_manager.py**: 8 次配置调用
  - 第75行: `self.config = get_unified_config()`
  - 第89行: `conv_config = self.config.get_conversation_config()`
  - 第127行: `query = self.config.get_config_value("database.queries.documents.get_draft_document")`
  - 第223行: `query = self.config.get_config_value("database.queries.focus_points_status.get_status")`
  - 第268行: `query = self.config.get_config_value("database.queries.focus_points_status.get_all_status")`

- **backend/agents/domain_classifier.py**: 4 次配置调用
  - 第122行: `config = config_service.get_llm_config_with_metadata("domain_classifier")`
  - 第127行: `unified_config = get_unified_config()`
  - 第248行: `config = config_service.get_llm_config_with_metadata("domain_classifier")`
  - 第253行: `unified_config = get_unified_config()`

- **backend/agents/dynamic_reply_generator.py**: 10 次配置调用
  - 第365行: `config = get_unified_config()`
  - 第400行: `config = get_unified_config()`
  - 第413行: `config = get_unified_config()`
  - 第419行: `config = get_unified_config()`
  - 第851行: `return get_unified_config().get_message_template(`

- **backend/agents/conversation_flow/state_manager.py**: 5 次配置调用
  - 第38行: `self.config = get_unified_config()`
  - 第45行: `self.p0_required = self.config.get_business_rule("business_rules.focus_point_priority.p0", True)`
  - 第46行: `self.p1_required = self.config.get_business_rule("business_rules.focus_point_priority.p1", True)`
  - 第47行: `self.p2_required = self.config.get_business_rule("business_rules.focus_point_priority.p2", False)`
  - 第198行: `self.config.get_database_query("focus_points.clear_processing"),`

- **backend/agents/conversation_flow/utils.py**: 4 次配置调用
  - 第17行: `_config = get_unified_config()`
  - 第29行: `config = config_service.get_llm_config_with_metadata("conversation_flow")`
  - 第36行: `return get_unified_config().get_threshold("business_rules.requirement_collection.completion_threshold", cls.COMPLETENESS_THRESHOLD)`
  - 第40行: `return get_unified_config().get_business_rule("retry.max_pending_attempts", cls.MAX_PENDING_ATTEMPTS)`

- **backend/agents/conversation_flow/message_processor.py**: 6 次配置调用
  - 第43行: `self.config = get_unified_config()`
  - 第80行: `error_message = self.config.get_message_template("error.message_processing")`
  - 第197行: `self.logger.error(self.config.get_message_template("error.processing", error=str(e)), exc_info=True)`
  - 第198行: `error_msg = self.config.get_message_template("error.general_unknown")`
  - 第340行: `error_message = self.config.get_message_template("error.request_processing")`

- **backend/agents/conversation_flow/core_refactored.py**: 58 次配置调用
  - 第175行: `self.unified_config_loader = get_unified_config()`
  - 第179行: `llm_config = self.config_service.get_llm_config("conversation_flow")`
  - 第225行: `return self.config_service.get_message_template(`
  - 第233行: `config = get_unified_config()`
  - 第325行: `"reply": self.config_service.get_message_template("error.system")`

- **backend/agents/strategies/capabilities_strategy.py**: 8 次配置调用
  - 第29行: `self.config = get_unified_config()`
  - 第42行: `self.config.get_config_value("strategy_templates.capabilities_strategy.templates.core_abilities") or`
  - 第46行: `self.config.get_config_value("strategy_templates.capabilities_strategy.templates.specific_services") or`
  - 第50行: `self.config.get_config_value("strategy_templates.capabilities_strategy.templates.interaction_features") or`
  - 第57行: `self.config.get_config_value("strategy_templates.capabilities_strategy.detailed_explanations") or`

- **backend/agents/strategies/emotional_support_strategy.py**: 5 次配置调用
  - 第33行: `self.config = get_unified_config()`
  - 第84行: `config_response_templates = self.config.get_config_value("strategy_templates.emotional_support_strategy.templates.response_templates")`
  - 第103行: `config_positive_guidance = self.config.get_config_value("strategy_templates.emotional_support_strategy.templates.positive_guidance")`
  - 第236行: `response_template=get_unified_config().get_config_value("message_templates.emotional_support_strategy.fallback_response"),`
  - 第371行: `base_response = get_unified_config().get_config_value("message_templates.emotional_support_strategy.default_understanding")`

- **backend/agents/strategies/greeting_strategy.py**: 9 次配置调用
  - 第27行: `self.config = get_unified_config()`
  - 第34行: `self.strategy_config = self.config.get_config_value("strategy_templates.greeting_strategy") or {}`
  - 第121行: `fallback_greeting = self.config.get_message_template("greeting.simple")`
  - 第196行: `return self.config.get_message_template(template_key)`
  - 第199行: `return self.config.get_message_template(template_key)`

- **backend/agents/strategies/requirement_strategy.py**: 6 次配置调用
  - 第27行: `self.config = get_unified_config()`
  - 第54行: `config_questions = self.config.get_config_value("strategy_templates.requirement_strategy.templates.collection_questions")`
  - 第65行: `self.config.get_config_value("strategy_templates.requirement_strategy.templates.confirmation_templates") or`
  - 第104行: `self.config.get_config_value("strategy_templates.requirement_strategy.patterns.requirement_patterns") or`
  - 第324行: `max_score = self.config.get_config_value("thresholds.strategy.requirement.max_keyword_score")`

- **backend/agents/strategies/fallback_strategy.py**: 5 次配置调用
  - 第29行: `self.config = get_unified_config()`
  - 第36行: `config_fallback_templates = self.config.get_config_value("strategy_templates.fallback_strategy.templates.fallback_templates")`
  - 第47行: `config_scenario_guides = self.config.get_config_value("strategy_templates.fallback_strategy.templates.scenario_guides")`
  - 第57行: `config_error_templates = self.config.get_config_value("strategy_templates.fallback_strategy.templates.error_templates")`
  - 第168行: `config = get_unified_config()`

- **backend/utils/intent_manager.py**: 4 次配置调用
  - 第77行: `self.logger.info(f"配置版本: {self.config.get('intent_system', {}).get('version', 'unknown')} [重复 {load_repeat_count - 1} 次]")`
  - 第80行: `self.logger.info(f"配置版本: {self.config.get('intent_system', {}).get('version', 'unknown')}")`
  - 第247行: `config = get_unified_config()`
  - 第279行: `intent_system = self.config.get('intent_system', {})`

- **backend/safety/content_moderation.py**: 9 次配置调用
  - 第86行: `self.config = get_unified_config()`
  - 第105行: `self.enabled = self.config.get_config_value("security.content_moderation.enabled", True)`
  - 第107行: `self.config.get_config_value("security.content_moderation.default_action", "WARN")`
  - 第111行: `actions_config = self.config.get_config_value("security.content_moderation.actions", {})`
  - 第121行: `masking_config = self.config.get_config_value("security.content_moderation.masking", {})`

- **backend/api/main.py**: 14 次配置调用
  - 第123行: `default_config = config_service.get_llm_config_with_metadata("default")`
  - 第256行: `config = get_unified_config()`
  - 第416行: `config_loader = get_unified_config()`
  - 第491行: `unified_config = get_unified_config()`
  - 第652行: `"response": get_unified_config().get_config_value("message_templates.error.request_timeout"),`

- **backend/data/db/admin_manager.py**: 7 次配置调用
  - 第25行: `self.config = get_unified_config()`
  - 第83行: `query = self.config.get_config_value("database.queries.admin_users.count_by_role")`
  - 第95行: `query = self.config.get_config_value("database.queries.admin_users.create_user")`
  - 第133行: `query = self.config.get_config_value("database.queries.admin_users.update_last_login")`
  - 第222行: `query = self.config.get_config_value("database.queries.statistics.count_documents_by_user")`

- **backend/data/db/focus_point_manager.py**: 11 次配置调用
  - 第37行: `get_unified_config().get_database_query("conversations.check_exists"),`
  - 第45行: `get_unified_config().get_database_query("conversations.create_new"),`
  - 第77行: `get_unified_config().get_database_query("focus_points.check_exists"),`
  - 第96行: `get_unified_config().get_database_query("focus_points.batch_insert"),`
  - 第125行: `get_unified_config().get_database_query("focus_points.get_status"),`

- **backend/data/db/message_manager.py**: 9 次配置调用
  - 第37行: `get_unified_config().get_database_query("conversations.check_exists"),`
  - 第45行: `get_unified_config().get_database_query("conversations.create_new"),`
  - 第77行: `get_unified_config().get_database_query("messages.save_message"),`
  - 第83行: `get_unified_config().get_database_query("conversations.update_last_activity"),`
  - 第110行: `get_unified_config().get_database_query("messages.get_conversation_history_limited"),`

- **backend/data/db/document_manager.py**: 9 次配置调用
  - 第33行: `query = get_unified_config().get_database_query("documents.get_content")`
  - 第48行: `query = get_unified_config().get_database_query("documents.get_by_conversation")`
  - 第63行: `query = get_unified_config().get_database_query("documents.get_by_conversation")`
  - 第101行: `query = get_unified_config().get_database_query("documents.save_document")`
  - 第152行: `query = get_unified_config().get_database_query("documents.update_content")`

- **backend/data/db/conversation_manager.py**: 6 次配置调用
  - 第50行: `get_unified_config().get_database_query("conversations.get_active"),`
  - 第79行: `get_unified_config().get_database_query("conversations.get_expired"),`
  - 第115行: `get_unified_config().get_database_query("conversations.delete_expired"),`
  - 第142行: `get_unified_config().get_database_query("backup.export_conversation"),`
  - 第174行: `get_unified_config().get_database_query("conversations.update_last_activity"),`

- **backend/handlers/conversation_handler.py**: 11 次配置调用
  - 第76行: `self.config = get_unified_config()`
  - 第190行: `content = self.config.get_message_template("system.session.reset_complete", session_id=session_id)`
  - 第237行: `prompt_instruction = self.config.get_message_template("prompts.restart.instruction")`
  - 第245行: `content = self.config.get_message_template("system.session.reset_complete")`
  - 第289行: `content = self.config.get_message_template("confirmation.document_finalized")`

- **backend/handlers/composite_handler.py**: 5 次配置调用
  - 第217行: `fallback_message = get_unified_config().get_config_value("message_templates.composite_handler.continue_collecting")`
  - 第277行: `config = config_service.get_llm_config_with_metadata("composite_handler")`
  - 第282行: `unified_config = get_unified_config()`
  - 第406行: `fallback_message = get_unified_config().get_config_value("message_templates.composite_handler.continue_collecting")`
  - 第427行: `fallback_message = get_unified_config().get_config_value("message_templates.composite_handler.continue_collecting")`

- **backend/handlers/general_request_handler.py**: 7 次配置调用
  - 第19行: `self.config = get_unified_config()`
  - 第163行: `return self.config.get_message_template("clarification.need_more_info")`
  - 第180行: `result = self.config.get_message_template("greeting.general_assistant")`
  - 第440行: `result = self.config.get_message_template("clarification.detailed_clarification")`
  - 第552行: `result = self.config.get_message_template("introduction.youji_platform")`

- **backend/services/conversation_history_service.py**: 9 次配置调用
  - 第35行: `config = get_unified_config()`
  - 第58行: `self.config = get_unified_config()`
  - 第62行: `max_turns=self.config.get_threshold("system.performance.max_conversation_turns", 15),`
  - 第63行: `max_message_length=self.config.get_threshold("system.performance.max_message_length", 200),`
  - 第122行: `turns = self.config.get_threshold("performance.retry.default", 5)`

## 📁 配置文件问题

- **missing_config_file**: 缺少必需的配置文件: business_rules.yaml
- **missing_config_file**: 缺少必需的配置文件: thresholds.yaml

## 🗑️ 未使用的配置键

- `security.content_moderation`
- `strategies.COLLECTING_INFO.process_answer.confused.action`
- `strategies.GLOBAL.unknown.anxious.prompt_instruction`
- `strategy_templates.emotional_support_strategy.templates.response_templates.negative.sadness`
- `domain_selection_mapping.logging.debug.no_active_state`
- `message_templates.system.session.restart_request`
- `domain_selection_mapping.number_mapping.2.domain_id`
- `domain_selection_mapping.logging`
- `database.queries.messages.get_recent_messages`
- `strategies.GLOBAL.business_requirement.marketing_requirement`
- `message_templates.error.document_modification`
- `llm.scenario_params.greeting_generator`
- `system.decision_engine.cache_ttl`
- `strategies.GLOBAL.request_clarification.question_clarification.anxious`
- `domain_selection_mapping.business.focus_points.progress_awareness.three_quarters_complete`
- `domain_selection_mapping.fallback.processing_failed`
- `strategies.COLLECTING_INFO.modify`
- `message_reply_system.generators.greeting_generator.temperature`
- `strategies.DOCUMENTING.restart.neutral.priority`
- `business_rules.document_confirmation.confirmation_keywords`
- `intent_system.intents.request_clarification.supported_states`
- `message_reply_system.generators.introduction_generator`
- `message_reply_system.generators.empathy_generator.max_tokens`
- `domain_selection_mapping.number_mapping.1.domain_name`
- `domain_selection_mapping.logging.info.domain_transition_collecting`
- `strategy_keywords.fallback_strategy`
- `llm.models.doubao-1.5-Lite.api_key`
- `performance.monitoring.log_slow_queries`
- `strategy_templates.greeting_strategy.parameters.confidence_factors.message_length.short`
- `llm.scenario_mapping.optimized_question_generation`
- `strategies.DOCUMENTING.modify.neutral.action`
- `database.queries.summaries.get_summary`
- `intent_system.intents.feedback`
- `strategies.GLOBAL.ask_question.technical_question.confused.prompt_instruction`
- `knowledge_base.features.document_ingestion`
- `strategies.GLOBAL.business_requirement.software_development.neutral.priority`
- `llm.scenario_params.conversation_flow`
- `conversation.keyword_acceleration.rules.business_requirement.intent`
- `intent_system.intents.provide_information.priority`
- `strategies.IDLE.ask_question.neutral.prompt_instruction`
- `intent_system.intents.modify.action`
- `domain_selection_mapping.chat.friendly`
- `thresholds.requirement_completeness`
- `domain_selection_mapping.logging.warning.intent_config_not_found`
- `strategy_templates.knowledge_base_strategy.templates.search_prompts.specific`
- `database.queries.summaries`
- `intent_system.intents.provide_information.description`
- `domain_selection_mapping.keyword_mapping.界面.domain_name`
- `message_reply_system.generators.chat_generator`
- `message_reply_system.generators.clarification_generator.agent_name`
- `message_templates.clarification.unclear_intent`
- `security.access_control.rate_limiting`
- `intent_system.intents.search_knowledge_base.priority`
- `message_templates.system.document.project_name_default`
- `knowledge_base.performance.max_concurrent_queries`
- `domain_selection_mapping.business.suggestions.no_pending`
- `strategies.DOCUMENTING.modify.neutral.prompt_instruction`
- `message_templates.requirement_gathering`
- `security.data_protection.encrypt_sensitive`
- `message_templates.greeting.happy_to_help`
- `message_reply_system.generators.chat_generator.max_tokens`
- `strategies.GLOBAL.ask_question.neutral.prompt_instruction`
- `security.input_validation.max_file_size`
- `llm.models.qwen-max-latest.model_name`
- `message_reply_system.generators.capabilities_generator.max_tokens`
- `conversation.states.default`
- `message_templates.error.document_modification_failed`
- `database.queries.documents.update_status`
- `thresholds.business`
- `domain_selection_mapping.logging.info.problem_statement_recorded`
- `strategies.COLLECTING_INFO.complete.positive`
- `strategies.GLOBAL.business_requirement.neutral.priority`
- `domain_selection_mapping.unknown_action`
- `strategies.GLOBAL.request_clarification.neutral.priority`
- `strategies.GLOBAL.complete.positive.prompt_instruction`
- `system.logging.max_file_size`
- `error_fallback_templates`
- `domain_selection_mapping.capabilities.full`
- `domain_selection_mapping.business.suggestions.smart_tips.user_experience`
- `intent_system.intents.provide_information.supported_states`
- `strategies.GLOBAL.unknown.neutral.priority`
- `knowledge_base.role_filters`
- `strategies.COLLECTING_INFO._state_config.use_simplified_logic`
- `domain_selection_mapping.prompts.chat`
- `strategies.DOCUMENTING.confirm.neutral.prompt_instruction`
- `strategies.GLOBAL.ask_question.technical_question.confused.action`
- `strategy_templates.greeting_strategy.greeting_type_detection.time_based_indicators`
- `intent_system.state_transitions.COLLECTING_INFO.unknown`
- `domain_selection_mapping.prompts.capabilities.instruction`
- `strategy_templates.knowledge_base_strategy.templates`
- `strategies.error_handling.retry_on_failure`
- `strategy_templates.greeting_strategy.response_mapping.emotion_based`
- `llm.scenario_params.domain_classifier`
- `message_reply_system.categories.completion`
- `strategies.COLLECTING_INFO.ask_question.neutral.action`
- `message_templates.greeting.professional`
- `strategies.GLOBAL.business_requirement`
- `strategies.COLLECTING_INFO.process_answer.confused.prompt_instruction`
- `thresholds.performance.retry`
- `database.queries.messages.delete_conversation_messages`
- `strategies.COLLECTING_INFO.reject.negative.prompt_instruction`
- `domain_selection_mapping.exception.rephrase.detailed`
- `strategy_templates.greeting_strategy.greeting_type_detection.casual_indicators`
- `message_templates.error.request_processing`
- `thresholds.performance.retry.llm_request`
- `message_templates.error.unknown_error`
- `strategies.COLLECTING_INFO.provide_information`
- `domain_selection_mapping.number_mapping.1`
- `domain_selection_mapping.number_mapping.2`
- `strategies.GLOBAL.ask_question.anxious.prompt_instruction`
- `domain_selection_mapping.number_mapping.三.domain_id`
- `conversation.transitions.CATEGORY_CLARIFICATION.clarification_success`
- `thresholds.confidence.domain_classification`
- `strategies.GLOBAL.business_requirement.marketing_requirement.anxious`
- `message_reply_system.categories.error.description`
- `llm.scenario_params.domain_guidance_generator.temperature`
- `domain_selection_mapping.keyword_mapping.营销`
- `domain_selection_mapping.exception`
- `intent_system.intents.search_knowledge_base.examples`
- `strategies.DOCUMENTING.reject.negative.action`
- `security.data_protection.mask_personal_info`
- `message_reply_system.enable_a_b_testing`
- `domain_selection_mapping.logging.debug.intent_keyword_match`
- `message_templates.error.safety.pii_warning`
- `domain_selection_mapping.keyword_mapping.海报.domain_name`
- `message_reply_system.generators.greeting_generator.max_tokens`
- `domain_selection_mapping.keyword_mapping.合同`
- `database.queries.conversations.update_last_activity`
- `strategy_templates.emotional_support_strategy.templates.response_templates.confused`
- `intent_system.intents.process_query.examples`
- `domain_selection_mapping.conversation.default.requirement_prompt`
- `performance.agent_cache.enable_session_cache`
- `thresholds.memory_usage_limit`
- `domain_selection_mapping.keyword_mapping.小程序`
- `domain_selection_mapping.conversation.default`
- `business_templates.acknowledge_and_redirect`
- `thresholds.security`
- `domain_selection_mapping.number_mapping.1.domain_id`
- `strategies.COLLECTING_INFO.modify.neutral.action`
- `strategies.GLOBAL.request_clarification.neutral.action`
- `llm.default_model`
- `domain_selection_mapping.logging.error.format_focus_points_failed`
- `message_templates.error.system`
- `llm.models.qwen-max-latest.top_p`
- `strategies.COLLECTING_INFO.provide_information.positive.action`
- `strategy_templates.knowledge_base_strategy.patterns`
- `intent_system.intents.greeting`
- `intent_system.intents.feedback.examples`
- `database.queries.messages.get_messages_by_focus`
- `strategies.IDLE.ask_question.requirement_question.positive`
- `strategy_keywords.greeting_strategy`
- `intent_system.intents.business_requirement.priority`
- `intent_system.intents.system_capability_query.supported_states`
- `llm.scenario_params.default.temperature`
- `intent_system.decision_rules.confidence_thresholds.low`
- `message_reply_system.generators.default_generator.agent_name`
- `message_reply_system.analytics.enabled`
- `llm.models.openrouter-gemini-flash.api_key`
- `message_templates.greeting.welcome_service`
- `business_rules.requirement_collection.min_focus_points`
- `message_reply_system.categories.confirmation.description`
- `llm.scenario_params.default_generator.timeout`
- `domain_selection_mapping.conversation.modification`
- `strategies.GLOBAL.provide_information.neutral.prompt_instruction`
- `message_reply_system.categories.clarification.description`
- `domain_selection_mapping.business.suggestions.general_teaching_guidance`
- `intent_system.intents.domain_specific_query.description`
- `performance.agent_cache.max_cached_sessions`
- `strategies.DOCUMENTING.general_request.neutral.prompt_instruction`
- `message_templates.error.general_problem`
- `domain_selection_mapping.business.question`
- `strategies.GLOBAL.provide_information.confused.priority`
- `conversation.transitions.COLLECTING_INFO.confirm`
- `message_reply_system.generators.introduction_generator.max_tokens`
- `domain_selection_mapping.keyword_mapping.推广.domain_id`
- `message_reply_system.generators.capabilities_generator.agent_name`
- `intent_system.intents.process_answer.action`
- `strategies.DOCUMENTING._state_config.use_simplified_logic`
- `message_templates.error.safety.blocked_violence`
- `strategy_keywords`
- `strategy_templates.greeting_strategy.parameters.confidence_factors.intent_match`
- `domain_selection_mapping.keyword_mapping.网站.domain_id`
- `thresholds.confidence.high`
- `llm.models.qwen-max-latest.api_base`
- `database.queries.documents.save_document`
- `conversation.states`
- `strategies.COLLECTING_INFO.skip`
- `intent_system.intents.unknown.examples`
- `business_rules.quality_control`
- `strategies.GLOBAL.request_clarification.term_clarification.confused.prompt_instruction`
- `message_reply_system.a_b_testing.test_groups.greeting.traffic_split`
- `domain_selection_mapping.user_interaction.defaults`
- `strategies.GLOBAL.request_clarification.question_clarification.neutral.action`
- `message_templates.guidance.proactive_suggestions.completion_guidance`
- `strategies.DOCUMENTING.confirm.positive`
- `conversation.transitions.CATEGORY_CLARIFICATION.clarification_failed`
- `database.queries.concern_point_coverage.update_status`
- `thresholds.llm.short_timeout`
- `strategy_templates.capabilities_strategy.detailed_explanations.continuous_support`
- `message_reply_system.categories.error.enabled`
- `business_rules.retry.backoff_factor`
- `database.queries.focus_points_status`
- `message_reply_system.a_b_testing`
- `system.language`
- `domain_selection_mapping.business.focus_points.skip_processing`
- `strategy_keywords.greeting_strategy.casual`
- `intent_system.intents.modify.supported_states`
- `intent_system.intents.provide_information.examples`
- `strategy_templates.fallback_strategy.templates.scenario_guides.information_search`
- `database.queries.focus_points.reset_status`
- `thresholds.business.user_satisfaction_threshold`
- `domain_selection_mapping.keyword_mapping.活动.domain_id`
- `message_templates.system.error`
- `domain_selection_mapping.logging.warning`
- `intent_system.intents.ask_introduction.action`
- `strategies.GLOBAL.unknown.neutral.action`
- `domain_selection_mapping.keyword_mapping.海报.domain_id`
- `llm.scenario_params.intent_recognition.timeout`
- `message_reply_system.generators.clarification_generator`
- `message_templates.system.document.generated`
- `strategies.IDLE.business_requirement.positive.prompt_instruction`
- `strategy_templates.knowledge_base_strategy.templates.search_prompts.complex`
- `domain_selection_mapping.business.suggestions.general_suggestions`
- `business_rules.action_handlers.handler_classes.ConversationHandler`
- `llm.scenario_params.structured_intent_classification.timeout`
- `llm.models.qwen-plus`
- `database.queries.documents.update_content`
- `domain_selection_mapping.keyword_mapping.平面设计.domain_name`
- `strategy_templates.emotional_support_strategy.templates.response_templates.positive`
- `strategies.IDLE.ask_question.requirement_question`
- `knowledge_base_keywords.support`
- `llm.scenario_params.document_generation`
- `llm.models.qwen-plus.api_base`
- `strategies.COLLECTING_INFO.provide_information.answer_question.positive`
- `domain_selection_mapping.keyword_mapping.平面设计.domain_id`
- `strategy_templates.requirement_strategy.templates.collection_questions.development`
- `llm.models.openrouter-gemini-flash.max_tokens`
- `message_reply_system.categories.confirmation.priority`
- `strategy_templates.greeting_strategy.parameters.confidence_factors.message_length.very_short`
- `strategies.COLLECTING_INFO.complete`
- `domain_selection_mapping.user_interaction.redirect`
- `strategies.IDLE._state_config.description`
- `llm.scenario_params.empathy_generator.temperature`
- `strategies.GLOBAL.provide_information.anxious`
- `message_templates.guidance.specific_requirement_help`
- `domain_selection_mapping.prompts.chat.instruction`
- `knowledge_base.chroma_db.embedding_model`
- `message_templates.introduction.youji_platform`
- `knowledge_base_keywords.features`
- `llm.models.qwen-turbo-latest.temperature`
- `intent_system.decision_rules.confidence_thresholds`
- `domain_selection_mapping.requirement_collection.contextual_suggestions.marketing_project`
- `message_reply_system.categories.confirmation.enabled`
- `security.input_validation.max_length`
- `llm.models.qwen-intent.max_retries`
- `intent_system.intents.greeting.action`
- `message_reply_system.max_retry_attempts`
- `database.queries.documents.get_active_document`
- `knowledge_base_keywords.pricing`
- `strategy_templates.fallback_strategy.templates.fallback_templates.capability_hint`
- `domain_selection_mapping.number_mapping.三`
- `knowledge_base.document_processing.max_chunks_per_doc`
- `strategy_templates.emotional_support_strategy.templates`
- `security.content_moderation.logging.log_violations`
- `strategies.GLOBAL.request_clarification.question_clarification.anxious.priority`
- `domain_selection_mapping.business.focus_points.progress_awareness`
- `strategies.GLOBAL.business_requirement.design_requirement.neutral.priority`
- `llm.scenario_mapping.llm_service`
- `message_templates.system.action_executor.failed`
- `strategies.COLLECTING_INFO.provide_information.neutral.priority`
- `message_reply_system.categories.confirmation`
- `strategy_keywords.emotional_support_strategy.positive`
- `performance.monitoring.enabled`
- `intent_system.intents.process_answer.description`
- `message_reply_system.generators.default_generator.fallback_template`
- `strategies.COLLECTING_INFO.request_clarification`
- `knowledge_base.retrieval.max_context_length`
- `domain_selection_mapping.keyword_mapping.logo.domain_name`
- `llm.scenario_params.information_extractor.temperature`
- `llm.scenario_mapping.conversation_flow`
- `keyword_rules.ask_question`
- `message_reply_system.generators.default_generator.enabled`
- `thresholds.max_conversation_turns`
- `database.queries.focus_point_definitions.get_by_category`
- `strategies.GLOBAL.unknown.confused.prompt_instruction`
- `business_rules.action_handlers.handler_classes.DocumentHandler`
- `message_reply_system.generators.clarification_generator.max_tokens`
- `knowledge_base.logging.log_results`
- `llm.default_params.top_p`
- `strategies.COLLECTING_INFO.complete.neutral.action`
- `message_reply_system.categories.guidance`
- `domain_selection_mapping.help`
- `intent_system.intents.general_chat.examples`
- `strategy_templates.capabilities_strategy.detailed_explanations.document_generation`
- `strategies.COLLECTING_INFO.provide_information.answer_question.neutral.priority`
- `message_reply_system.categories.guidance.description`
- `domain_selection_mapping.keyword_mapping.推广.domain_name`
- `message_templates.error.safety`
- `domain_selection_mapping.keyword_mapping.广告`
- `domain_selection_mapping.logging.warning.llm_no_valid_unknown`
- `strategies.COLLECTING_INFO.provide_information.neutral.action`
- `performance.agent_cache.enable_component_cache`
- `database.queries`
- `strategies.COLLECTING_INFO.request_clarification.confused.priority`
- `strategy_templates.greeting_strategy.parameters.message_length_thresholds.very_short`
- `domain_selection_mapping.user_interaction.defaults.user_skip_choice`
- `message_reply_system.categories.greeting.priority`
- `strategies.COLLECTING_INFO.complete.neutral`
- `integrations.knowledge_base.enabled`
- `llm.models.qwen-max-latest.max_retries`
- `strategies.GLOBAL.greeting.neutral`
- `domain_selection_mapping.keyword_mapping.ui`
- `strategies.GLOBAL.request_clarification.question_clarification.neutral.prompt_instruction`
- `conversation.transitions.COLLECTING_INFO`
- `knowledge_base.logging.level`
- `message_templates.guidance.initial`
- `thresholds.short_timeout`
- `llm.scenario_params.apology_generator.max_tokens`
- `system.logging.backup_count`
- `database.tables.conversations.auto_cleanup`
- `strategies.COLLECTING_INFO.confirm`
- `llm.scenario_params.default.api_key`
- `message_reply_system.categories.clarification`
- `domain_selection_mapping.keyword_mapping.app.domain_name`
- `intent_system.intents.feedback.action`
- `message_templates.composite_handler`
- `strategies.GLOBAL.business_requirement.software_development.neutral.prompt_instruction`
- `strategy_keywords.emotional_support_strategy.intensity`
- `strategies.DOCUMENTING.confirm.neutral`
- `strategies.GLOBAL.business_requirement.marketing_requirement.anxious.action`
- `llm.scenario_params.category_classifier`
- `strategies.GLOBAL.ask_question.confused.priority`
- `message_templates.state_machine`
- `intent_system.intents.emotional_support.priority`
- `strategies.DOCUMENTING.general_request.neutral.priority`
- `llm.scenario_params.domain_classifier.timeout`
- `database.queries.focus_points.check_exists`
- `database.queries.focus_points.get_single_status`
- `llm.models.doubao-pro-32k.timeout`
- `message_reply_system.generators.empathy_generator`
- `domain_selection_mapping.keyword_mapping.ui.domain_id`
- `message_reply_system.generators.greeting_generator.agent_name`
- `strategy_templates.greeting_strategy.parameters.confidence_factors.message_length.long`
- `strategies.GLOBAL.request_clarification.neutral`
- `strategies.COLLECTING_INFO.reject.neutral.priority`
- `strategies.COLLECTING_INFO.provide_information.anxious`
- `intent_system.intents.emotional_support.examples`
- `domain_selection_mapping.requirement_collection.contextual_suggestions.software_development`
- `intent_system.state_transitions.IDLE.emotional_support`
- `strategies.GLOBAL.reset.neutral.priority`
- `domain_selection_mapping.number_mapping.二`
- `security.content_moderation.logging.include_original_text`
- `thresholds.confidence.low`
- `strategies.IDLE.business_requirement.neutral.priority`
- `llm.models.deepseek-chat`
- `llm.models.doubao-pro-32k.max_retries`
- `strategies.COLLECTING_INFO.provide_information.answer_question.positive.priority`
- `message_templates.system.fallback`
- `thresholds.quality.completeness_threshold`
- `strategies.COLLECTING_INFO.reject.negative.action`
- `system_capability_keywords.general_inquiry`
- `llm.models.openrouter-gemini-flash.temperature`
- `strategy_templates.knowledge_base_strategy.templates.response_templates.registration`
- `security.input_validation`
- `llm.default_params.max_tokens`
- `message_reply_system.categories.completion.enabled`
- `strategies.error_handling.graceful_degradation`
- `knowledge_base_keywords.product_info`
- `message_reply_system.generators.empathy_generator.fallback_template`
- `security.content_moderation.actions.pii_patterns`
- `intent_system.intents.process_answer.examples`
- `strategies.GLOBAL.confirm`
- `message_templates.system.welcome`
- `thresholds.completion_threshold`
- `intent_system.intents.request_clarification`
- `knowledge_base.role_filters.enabled`
- `metadata.version`
- `database.queries.concern_point_coverage.get_by_conversation`
- `domain_selection_mapping.keyword_mapping.平面设计`
- `knowledge_base.chroma_db`
- `intent_system.intents.request_clarification.action`
- `message_templates.knowledge_base_handler`
- `system.fallback_enabled`
- `domain_selection_mapping.keyword_mapping.网站.domain_name`
- `business_templates.rephrase_and_inquire`
- `strategies.GLOBAL.ask_question.requirement_question.anxious`
- `security.access_control.max_requests_per_minute`
- `intent_system.intents.ask_question.examples`
- `message_templates.system.state`
- `intent_keywords.ask_introduction`
- `strategies.GLOBAL.business_requirement.marketing_requirement.anxious.prompt_instruction`
- `intent_system.intents.ask_introduction.supported_states`
- `thresholds.llm.long_timeout`
- `strategies.GLOBAL.request_clarification.anxious`
- `strategies.GLOBAL.request_clarification.term_clarification.neutral.action`
- `business_templates.general_chat`
- `strategy_templates.greeting_strategy.response_mapping.time_based.morning`
- `strategies.GLOBAL.greeting.positive.priority`
- `domain_selection_mapping.conversation.restart.confirmation`
- `strategies.GLOBAL.ask_question.requirement_question.neutral.priority`
- `strategies.COLLECTING_INFO.provide_information.answer_question.positive.prompt_instruction`
- `thresholds.quality.relevance_threshold`
- `llm.scenario_params.greeting_generator.max_tokens`
- `intent_system.state_transitions.COLLECTING_INFO.request_clarification`
- `thresholds.confidence.intent_recognition`
- `message_reply_system.categories.clarification.enabled`
- `message_reply_system.generators.greeting_generator.description`
- `message_reply_system.generators.default_generator.temperature`
- `message_reply_system.categories.completion.fallback_template`
- `llm.models.deepseek-chat.max_tokens`
- `strategies.GLOBAL.greeting.positive.prompt_instruction`
- `strategy_templates.fallback_strategy.templates.scenario_guides.consultation`
- `llm.models.qwen-plus.timeout`
- `domain_selection_mapping.number_mapping.5.domain_id`
- `thresholds.llm`
- `strategies.GLOBAL.ask_question.requirement_question.neutral.action`
- `strategy_templates.capabilities_strategy.detailed_explanations.consultation`
- `strategies.IDLE.business_requirement.anxious.action`
- `strategies.GLOBAL.request_clarification.term_clarification`
- `intent_system.intents.search_knowledge_base`
- `strategy_templates.greeting_strategy.parameters.message_length_thresholds`
- `strategies.IDLE.greeting.neutral.priority`
- `domain_selection_mapping.logging.error.knowledge_base_not_initialized`
- `strategies.GLOBAL.complete`
- `intent_system.intents.restart.supported_states`
- `strategies.COLLECTING_INFO.skip.neutral.prompt_instruction`
- `strategy_templates.capabilities_strategy.detailed_explanations.knowledge_search`
- `strategies.GLOBAL.business_requirement.marketing_requirement.anxious.priority`
- `strategies.IDLE.business_requirement.neutral`
- `message_templates.knowledge_base_strategy`
- `intent_system.intents.emotional_support.description`
- `thresholds.confidence.structured_classification`
- `message_reply_system.enable_analytics`
- `compatibility.legacy_mapping.usage`
- `domain_selection_mapping.logging.debug.intent_llm_analysis`
- `strategies.COLLECTING_INFO.complete.neutral.priority`
- `domain_selection_mapping.exception.general_request`
- `llm.scenario_params.empathy_generator.timeout`
- `database.queries.focus_point_definitions.get_by_focus_id`
- `strategies.COLLECTING_INFO.provide_information.confused.prompt_instruction`
- `domain_selection_mapping.logging.error.focus_points_not_found`
- `database.queries.concern_point_coverage`
- `strategies.GLOBAL.business_requirement.design_requirement.neutral.action`
- `strategies.GLOBAL.provide_information.positive`
- `llm.models.qwen-max-latest.api_key`
- `llm.models.deepseek-chat.temperature`
- `message_reply_system.generators.clarification_generator.temperature`
- `domain_selection_mapping.business.focus_points.progress_awareness.nearly_complete`
- `security.input_validation.forbidden_patterns`
- `domain_selection_mapping.user_interaction.processing.idle_modify_intent`
- `llm.scenario_mapping.information_extractor`
- `llm.models.doubao-1.5-Lite`
- `strategies.GLOBAL.request_clarification.neutral.prompt_instruction`
- `database.queries.sessions.update_session`
- `strategy_templates.emotional_support_strategy`
- `thresholds.performance.timeout.short`
- `strategies.GLOBAL.business_requirement.marketing_requirement.neutral.action`
- `message_reply_system.generators.greeting_generator.fallback_template`
- `domain_selection_mapping.keyword_mapping.法律.domain_id`
- `metadata.latest_changes`
- `emotion_keywords.confused`
- `domain_selection_mapping.keyword_mapping.网站`
- `strategy_templates.knowledge_base_strategy.templates.response_templates.how_to`
- `message_templates.requirement_gathering.fallback_question`
- `conversation.transitions.DIRECT_SELECTION.selection_made`
- `compatibility.legacy_mapping.pricing`
- `message_reply_system.categories.greeting`
- `knowledge_base.document_processing.chunk_size`
- `strategies.GLOBAL.reset.neutral.action`
- `intent_system.intents.ask_question`
- `message_templates.error.processing`
- `message_templates.system.session`
- `llm.models.deepseek-chat.top_p`
- `intent_system.state_transitions.IDLE.search_knowledge_base`
- `safety_keywords.self_harm`
- `intent_system.intents.unknown.description`
- `intent_system.intents.system_capability_query`
- `strategies.GLOBAL.business_requirement.neutral.prompt_instruction`
- `domain_selection_mapping.logging.info.focus_points_reset`
- `message_reply_system.fallback_enabled`
- `message_reply_system.generators.empathy_generator.agent_name`
- `strategy_templates.emotional_support_strategy.templates.response_templates.negative.frustration`
- `database.queries.backup.export_conversation`
- `conversation.keyword_acceleration.rules.emotional_support.intent`
- `domain_selection_mapping.requirement_collection.default_prompt`
- `domain_selection_mapping.business.question.optimization_context`
- `message_reply_system.generators.capabilities_generator.instruction`
- `domain_selection_mapping.business.focus_points.found_next`
- `intent_system.decision_rules`
- `llm.scenario_params.optimized_question_generation.max_tokens`
- `strategy_templates.fallback_strategy.templates.fallback_templates.encouragement`
- `strategies.GLOBAL.ask_question.neutral.action`
- `database.queries.conversations.get_expired`
- `strategies.COLLECTING_INFO.provide_information.answer_question`
- `system.logging`
- `domain_selection_mapping.business.focus_points.all_completed`
- `strategies.COLLECTING_INFO.provide_information.neutral.prompt_instruction`
- `strategies.GLOBAL.business_requirement.positive.prompt_instruction`
- `database.queries.admin_logs.insert_log`
- `conversation.transitions.DIRECT_SELECTION.restart`
- `strategy_templates.fallback_strategy`
- `database.queries.backup`
- `development.testing.use_test_db`
- `message_reply_system.generators.default_generator`
- `strategies.DOCUMENTING._state_config.fallback_action`
- `thresholds.performance.retry.api_call`
- `domain_selection_mapping.business.suggestions.smart_tips.timeline_planning`
- `intent_system.intents.ask_question.priority`
- `domain_selection_mapping.logging.info.intent_recognition_result`
- `llm.scenario_mapping.structured_intent_classification`
- `performance.monitoring.metrics_interval`
- `message_reply_system.categories.clarification.priority`
- `message_reply_system.categories.error.fallback_template`
- `strategies.DOCUMENTING.reject.negative.prompt_instruction`
- `compatibility`
- `strategies.DOCUMENTING._state_config.priority_order`
- `thresholds.quality.min_word_count`
- `strategies.IDLE.ask_question`
- `domain_selection_mapping.business.focus_points.searching_next`
- `message_templates.capabilities_strategy`
- `llm.models.qwen-turbo-latest.api_base`
- `domain_selection_mapping.keyword_mapping.ux.domain_id`
- `safety_keywords.sexual_content`
- `strategies.GLOBAL.provide_information.neutral.action`
- `strategies.GLOBAL.business_requirement.positive.action`
- `conversation.keyword_acceleration.rules.ask_question`
- `database.queries.focus_points.update_status`
- `llm.scenario_params.apology_generator`
- `security.content_moderation.actions.hate_speech`
- `intent_system.intents.provide_information`
- `domain_selection_mapping.help.simple`
- `message_templates.confirmation.restart`
- `llm.models.qwen-max-latest.temperature`
- `database.queries.admin_users`
- `strategy_templates.fallback_strategy.templates.fallback_templates.redirect`
- `domain_selection_mapping.requirement_collection.clarification`
- `metadata.created_date`
- `development.testing`
- `llm.models.doubao-pro-32k.temperature`
- `strategies.reply.default_template`
- `safety_keywords.violence`
- `intent_system.intents.ask_introduction.examples`
- `domain_selection_mapping.fallback.requirement_prompt`
- `llm.scenario_params.conversation_flow.max_tokens`
- `message_templates.system.state.update_success`
- `development.debug.mock_llm`
- `intent_system.state_transitions`
- `strategy_templates.greeting_strategy.parameters.message_length_thresholds.max_greeting_length`
- `domain_selection_mapping.logging.error.domain_guidance_generation_failed`
- `thresholds.business.response_time_threshold`
- `message_reply_system.generators.empathy_generator.enabled`
- `message_reply_system.generators.introduction_generator.fallback_template`
- `domain_selection_mapping.chat.simple`
- `llm.models.doubao-pro-32k.model_name`
- `domain_selection_mapping.number_mapping.4.domain_id`
- `thresholds.confidence.minimum`
- `performance.monitoring.slow_query_threshold`
- `strategies.GLOBAL.reject.negative.prompt_instruction`
- `domain_selection_mapping.keyword_mapping.app.domain_id`
- `domain_selection_mapping.user_interaction.defaults.detailed_requirement`
- `message_templates.confirmation.reset`
- `intent_system.intents.domain_specific_query.priority`
- `message_templates.greeting.standard`
- `message_templates.error.permission_denied`
- `message_templates.error.safety.warning_jailbreak`
- `strategies.GLOBAL.unknown.anxious`
- `strategies.GLOBAL.request_clarification.anxious.prompt_instruction`
- `message_templates.guidance.proactive_suggestions.welcome_with_examples`
- `strategies.GLOBAL.ask_question.confused.prompt_instruction`
- `message_reply_system.llm_timeout`
- `message_reply_system.generators.empathy_generator.instruction`
- `domain_selection_mapping.keyword_mapping.软件`
- `message_templates.system.document.project_name_template`
- `security.input_validation.enabled`
- `development.debug.log_responses`
- `thresholds.long_timeout`
- `integrations.external_apis.openai.timeout`
- `intent_system.intents.feedback.description`
- `performance.agent_cache.cleanup_interval_minutes`
- `strategy_templates.capabilities_strategy.patterns`
- `business_rules.quality_control.spam_detection_enabled`
- `performance.agent_cache`
- `message_templates.greeting.new_project`
- `llm.models.qwen-turbo-latest`
- `domain_selection_mapping.logging.info`
- `database.tables.conversations.cleanup_days`
- `database.queries.template_versions`
- `intent_system.intents.feedback.priority`
- `keyword_rules.modify`
- `llm.scenario_params.clarification_generator`
- `thresholds.similarity_threshold`
- `llm.models.doubao-1.5-Lite.provider`
- `llm.models.qwen-turbo-latest.top_p`
- `strategies.GLOBAL.ask_question.confused.action`
- `database.queries.messages.insert_message`
- `strategy_templates.greeting_strategy.parameters.confidence_factors.conversation_state`
- `domain_selection_mapping.number_mapping.一`
- `domain_selection_mapping.business.focus_points.progress_awareness.quarter_complete`
- `strategy_templates.knowledge_base_strategy.templates.response_templates.features`
- `llm.scenario_params.category_classifier.max_tokens`
- `strategies.COLLECTING_INFO.provide_information.confused.priority`
- `thresholds.performance.retry.max_attempts`
- `llm.scenario_params.domain_classification`
- `llm.scenario_params.default.model_name`
- `strategies.COLLECTING_INFO.modify.neutral.prompt_instruction`
- `knowledge_base_keywords.usage`
- `strategy_keywords.capabilities_strategy`
- `message_templates.confirmation.document_finalized`
- `strategy_templates.capabilities_strategy`
- `intent_system.intents.greeting.supported_states`
- `message_templates.system.session.no_domain_info`
- `intent_system.intents.business_requirement.action`
- `intent_system.state_transitions.IDLE.system_capability_query`
- `domain_selection_mapping.number_mapping.二.domain_name`
- `message_reply_system.generators.capabilities_generator.enabled`
- `domain_selection_mapping.keyword_mapping.知识产权`
- `message_reply_system.categories.empathy.enabled`
- `database.queries.admin_users.get_user_stats`
- `strategies.GLOBAL.request_clarification.anxious.priority`
- `domain_selection_mapping.number_mapping.2.domain_name`
- `domain_selection_mapping.business.focus_points.empty_list`
- `business_templates.empathy_and_clarify`
- `domain_selection_mapping.formatting.status`
- `intent_system.intents.business_requirement.description`
- `strategy_templates.capabilities_strategy.detailed_explanations.requirement_collection`
- `strategies.GLOBAL.request_clarification.question_clarification.neutral.priority`
- `domain_selection_mapping.introduction.simple`
- `strategy_templates.emotional_support_strategy.templates.response_templates.positive.happiness`
- `intent_system.intents.provide_information.action`
- `domain_selection_mapping.prompts.restart`
- `message_templates.error.general`
- `intent_system.intents.confirm.examples`
- `message_reply_system.analytics`
- `message_reply_system.version`
- `security.content_moderation.actions.self_harm`
- `message_templates.system.document.confirmation_prefix`
- `intent_system.intents.general_chat.description`
- `message_templates.guidance.proactive_suggestions.next_steps_suggestion`
- `database.connection.path`
- `domain_selection_mapping.formatting`
- `llm.scenario_params.clarification_generator.max_tokens`
- `message_reply_system.generators.introduction_generator.temperature`
- `domain_selection_mapping.business.suggestions.smart_tips.budget_consideration`
- `domain_selection_mapping.keyword_mapping.开发.domain_name`
- `strategy_keywords.fallback_strategy.context_clues`
- `knowledge_base.retrieval`
- `llm.scenario_mapping.category_classifier`
- `llm.models.qwen-intent.provider`
- `message_reply_system.generators.chat_generator.fallback_template`
- `domain_selection_mapping.formatting.history.empty`
- `strategies.DOCUMENTING.modify`
- `strategies.GLOBAL.business_requirement.anxious.prompt_instruction`
- `strategies.DOCUMENTING.restart`
- `intent_system.intents.confirm.supported_states`
- `database.queries.concern_point_coverage.get_coverage_by_id`
- `conversation.transitions.DOMAIN_CLARIFICATION.clarification_success`
- `message_reply_system.categories.completion.priority`
- `conversation.keyword_acceleration.rules.business_requirement.keywords`
- `strategies.COLLECTING_INFO.process_answer.anxious.action`
- `message_templates.conversation_handler`
- `message_templates.system.timeout`
- `database.queries.focus_points.reset_all_status`
- `llm.scenario_params.document_generator.timeout`
- `domain_selection_mapping.conversation.modification.idle_state_prompt`
- `strategies.GLOBAL.restart.neutral.prompt_instruction`
- `strategy_templates.greeting_strategy.response_mapping.emotion_based.positive`
- `safety_keywords.profanity`
- `message_reply_system.categories`
- `database.queries.session_states.get_current_state`
- `domain_selection_mapping.logging.error.document_modification_failed`
- `strategies.GLOBAL.ask_question.requirement_question.anxious.priority`
- `message_reply_system.categories.empathy.fallback_template`
- `thresholds.confidence.keyword_matching`
- `metadata.migration_sources`
- `intent_system.state_transitions.DOCUMENTING`
- `domain_selection_mapping.user_interaction.instructions.full_prompt_unknown`
- `thresholds.performance.timeout.api_request`
- `strategies.DOCUMENTING.general_request.neutral.action`
- `strategies.COLLECTING_INFO.skip.neutral.action`
- `strategies.IDLE`
- `conversation.keyword_acceleration.rules.ask_question.keywords`
- `intent_system.intents.request_clarification.examples`
- `message_reply_system.categories.greeting.fallback_template`
- `strategies.IDLE.business_requirement.anxious`
- `strategies.GLOBAL.ask_question.anxious.action`
- `intent_keywords.emotional_support`
- `thresholds.business.requirement_completion_threshold`
- `intent_system.intents.domain_specific_query.action`
- `llm.models.qwen-intent.api_key`
- `llm.scenario_params.category_classifier.timeout`
- `message_templates.error`
- `thresholds.security.burst_limit`
- `llm.scenario_params.default.timeout`
- `strategies.COLLECTING_INFO.reject.negative`
- `thresholds.quality.min_input_length`
- `thresholds.security.content_filter_threshold`
- `strategy_templates.greeting_strategy.parameters`
- `intent_system.intents.unknown.priority`
- `intent_system.decision_rules.default_action`
- `llm.models.qwen-turbo-latest.max_tokens`
- `strategies.IDLE.greeting.neutral`
- `domain_selection_mapping.prompts.introduction`
- `message_templates.guidance.proactive_suggestions`
- `strategies.COLLECTING_INFO`
- `llm.scenario_params.domain_classifier.max_tokens`
- `strategies.DOCUMENTING`
- `keyword_rules.business_requirement`
- `domain_selection_mapping.number_mapping.三.domain_name`
- `message_templates.system.document.finalized`
- `llm.scenario_mapping.greeting_generator`
- `strategies.IDLE.ask_question.requirement_question.neutral.action`
- `strategies.COLLECTING_INFO.provide_information.answer_question.anxious.priority`
- `business_rules.action_handlers`
- `strategies.GLOBAL.business_requirement.positive`
- `business_templates.reset_conversation`
- `thresholds.llm.default_temperature`
- `conversation.transitions.DOCUMENTING.restart`
- `thresholds.security.max_login_attempts`
- `message_reply_system.categories.guidance.enabled`
- `message_reply_system.generators.introduction_generator.enabled`
- `integrations`
- `message_templates.error.understanding_issue`
- `database.tables.documents.auto_backup`
- `conversation.transitions`
- `domain_selection_mapping.keyword_mapping.ui.domain_name`
- `strategies.GLOBAL.request_clarification.term_clarification.confused.priority`
- `thresholds.limits.log_max_entries`
- `conversation.keyword_acceleration.enabled`
- `database.connection`
- `llm.models.doubao-pro-32k.provider`
- `strategy_templates.fallback_strategy.templates.scenario_guides.technical_support`
- `strategies.COLLECTING_INFO.reject.neutral.prompt_instruction`
- `domain_selection_mapping.business`
- `message_templates.keyword_accelerator`
- `strategies.GLOBAL.business_requirement.anxious.action`
- `database.queries.sessions.get_session`
- `database.tables.documents.backup_interval`
- `strategies.DOCUMENTING._state_config`
- `message_templates.introduction`
- `domain_selection_mapping.number_mapping.五`
- `domain_selection_mapping.keyword_mapping.交互`
- `strategies.IDLE.ask_question.requirement_question.positive.priority`
- `domain_selection_mapping.fallback`
- `llm.models.doubao-1.5-Lite.max_retries`
- `strategies.IDLE.ask_question.requirement_question.positive.prompt_instruction`
- `conversation.transitions.IDLE.ask_question`
- `domain_selection_mapping.prompts.greeting.instruction`
- `intent_system.state_transitions.COLLECTING_INFO.restart`
- `conversation.transitions.COLLECTING_INFO.business_requirement`
- `knowledge_base.performance.cache_ttl`
- `message_templates.system.document.generation_start`
- `domain_selection_mapping.chat.general`
- `thresholds.llm.large_max_tokens`
- `message_templates.document_handler`
- `intent_system.intents.business_requirement.examples`
- `strategies.GLOBAL.ask_question.requirement_question`
- `llm.scenario_params.optimized_question_generation.timeout`
- `intent_system.intents.composite_knowledge_requirement.description`
- `llm.models.deepseek-chat.timeout`
- `strategies.COLLECTING_INFO.process_answer.confused`
- `business_templates`
- `strategies.COLLECTING_INFO.complete.positive.action`
- `strategies.GLOBAL.request_clarification.question_clarification.neutral`
- `message_templates.greeting.requirement_analyst`
- `strategies.IDLE.greeting.neutral.action`
- `domain_selection_mapping.logging.warning.domain_restore_failed`
- `intent_system.intents.confirm.description`
- `conversation.transitions.IDLE`
- `intent_system.intents.system_capability_query.action`
- `system.decision_engine.fallback_to_simplified`
- `llm.models.deepseek-chat.api_base`
- `strategies.GLOBAL.business_requirement.software_development`
- `database.queries.sessions.ensure_session_exists`
- `strategy_templates.requirement_strategy.templates.collection_questions.content`
- `intent_system.intents.process_query`
- `intent_system.state_transitions.DOCUMENTING.restart`
- `domain_selection_mapping.keyword_mapping.交互.domain_id`
- `intent_system.intents.restart.description`
- `strategies.GLOBAL.provide_information.confused.prompt_instruction`
- `database.queries.statistics`
- `llm.models.qwen-max-latest.provider`
- `system_fallback_templates.config_loading_fallback`
- `message_templates.error.knowledge_base_not_found`
- `domain_selection_mapping.logging.warning.conversation_history_failed`
- `conversation.keyword_acceleration.rules.greeting`
- `message_reply_system.categories.clarification.fallback_template`
- `database.queries.conversations.get_info`
- `intent_system.state_transitions.IDLE`
- `strategy_keywords.greeting_strategy.time_based`
- `message_templates.system.document.content_error`
- `thresholds.confidence_threshold`
- `domain_selection_mapping.keyword_mapping.海报`
- `message_templates.greeting.service_oriented`
- `llm.models.openrouter-gemini-flash.model_name`
- `strategies.GLOBAL.greeting.neutral.priority`
- `intent_system.intents.greeting.description`
- `intent_system.intents.unknown`
- `llm.scenario_params.llm_service.timeout`
- `strategies.GLOBAL.reset`
- `strategies.COLLECTING_INFO.request_clarification.confused.prompt_instruction`
- `llm.models.doubao-1.5-Lite.max_tokens`
- `strategies.IDLE.business_requirement.positive.action`
- `intent_system.state_transitions.COLLECTING_INFO.provide_information`
- `strategies.IDLE.business_requirement.neutral.prompt_instruction`
- `domain_selection_mapping.keyword_mapping.开发`
- `strategy_templates.requirement_strategy`
- `strategies.COLLECTING_INFO.provide_information.confused.action`
- `conversation.transitions.DOMAIN_CLARIFICATION`
- `database.queries.summaries.upsert_summary`
- `intent_system.intents.general_chat.supported_states`
- `llm.default_params.frequency_penalty`
- `intent_system.intents.system_capability_query.description`
- `database.queries.focus_points.insert_new`
- `domain_selection_mapping.business.suggestions.multiple_points`
- `strategy_templates.greeting_strategy.greeting_type_detection`
- `system.supported_languages`
- `thresholds.performance.retry.default`
- `llm.scenario_params.llm_service`
- `thresholds.security.rate_limit_per_hour`
- `strategies.COLLECTING_INFO.provide_information.positive`
- `domain_selection_mapping.prompts.empathy.default_instruction`
- `intent_system.state_transitions.IDLE.general_chat`
- `llm.models.qwen-plus.api_key`
- `message_reply_system.generators.capabilities_generator.description`
- `knowledge_base.features.mode_switching`
- `llm.scenario_params.domain_guidance_generator`
- `message_templates.greeting.simple`
- `llm.scenario_params.intent_recognition.temperature`
- `message_templates.guidance.default_requirement_prompt`
- `message_templates.clarification.default`
- `llm.models.qwen-turbo-latest.provider`
- `conversation.keyword_acceleration.rules.emotional_support`
- `llm.default_params.temperature`
- `intent_system.intents.confirm`
- `domain_selection_mapping.business.suggestions.single_point`
- `intent_system.intents.restart.action`
- `message_reply_system.categories.greeting.enabled`
- `domain_selection_mapping.keyword_mapping.合同.domain_name`
- `domain_selection_mapping.logging.error.determine_state`
- `database.queries.focus_points.get_definitions`
- `llm.models.doubao-1.5-Lite.timeout`
- `thresholds.max_document_size`
- `llm.scenario_mapping.domain_guidance_generator`
- `database.queries.concern_point_coverage.insert_coverage`
- `strategies.IDLE.business_requirement.positive`
- `strategy_templates.knowledge_base_strategy.templates.response_templates.faq`
- `strategies.COLLECTING_INFO.provide_information.answer_question.anxious.action`
- `domain_selection_mapping.business.question.optimization_failed`
- `strategies.GLOBAL.ask_question`
- `domain_selection_mapping.number_mapping.四`
- `intent_system.intents.confirm.priority`
- `compatibility.legacy_mapping.features`
- `strategies.GLOBAL.provide_information.anxious.priority`
- `llm.default_params.presence_penalty`
- `domain_selection_mapping.business.focus_points.skip_no_processing`
- `conversation.keyword_acceleration.rules.general_chat`
- `message_reply_system.categories.guidance.priority`
- `conversation.transitions.DOMAIN_CLARIFICATION.restart`
- `strategies.COLLECTING_INFO.skip.neutral`
- `message_reply_system.analytics.track_response_time`
- `conversation.transitions.IDLE.domain_classification_failed`
- `intent_system.intents.modify.description`
- `message_reply_system.generators.default_generator.description`
- `llm.models.qwen-plus.temperature`
- `database.queries.focus_points.complex_update`
- `intent_system.intents.emotional_support.supported_states`
- `strategies.IDLE._state_config.use_simplified_logic`
- `message_reply_system.categories.error.priority`
- `strategies.GLOBAL.business_requirement.anxious`
- `strategies.GLOBAL.ask_question.neutral`
- `thresholds.limits.default_max_items`
- `message_reply_system.analytics.export_interval_hours`
- `intent_system.intents.process_answer.supported_states`
- `strategies.DOCUMENTING.confirm.positive.action`
- `strategies.GLOBAL.ask_question.requirement_question.anxious.action`
- `strategies.COLLECTING_INFO.ask_question.neutral.prompt_instruction`
- `strategies.IDLE.business_requirement.anxious.priority`
- `message_reply_system.analytics.track_user_satisfaction`
- `domain_selection_mapping.business.focus_points.generation_failed`
- `conversation.transitions.COLLECTING_INFO.provide_information`
- `strategy_templates.knowledge_base_strategy`
- `strategies.COLLECTING_INFO.provide_information.anxious.action`
- `integrations.external_apis.openai.retry_attempts`
- `strategies.COLLECTING_INFO.request_clarification.neutral.action`
- `keyword_rules.confirm`
- `domain_selection_mapping.logging.warning.dynamic_reply_empty`
- `domain_selection_mapping.prompts.restart.instruction`
- `domain_selection_mapping.keyword_mapping.法律`
- `strategies.COLLECTING_INFO.modify.neutral.priority`
- `llm.models.qwen-max-latest.timeout`
- `llm.scenario_params.default_generator.max_tokens`
- `domain_selection_mapping.logging.debug`
- `strategy_templates.knowledge_base_strategy.templates.search_prompts.general`
- `llm.models.qwen-turbo-latest.model_name`
- `knowledge_base.document_processing.supported_formats`
- `thresholds.security.session_timeout`
- `database.queries.conversations.check_exists`
- `conversation.keyword_acceleration.rules.confirm`
- `strategies.COLLECTING_INFO.process_answer.confused.priority`
- `message_templates.system.processing.fallback_handling`
- `database.queries.focus_point_definitions`
- `intent_system.intents.emotional_support.action`
- `domain_selection_mapping.business.suggestions.no_suggestions`
- `llm.scenario_params.category_classifier.temperature`
- `strategies.GLOBAL.reject.neutral.priority`
- `message_templates.emotional_support_strategy`
- `intent_system.intents.business_requirement.supported_states`
- `strategy_templates.emotional_support_strategy.templates.response_templates.negative.anxiety`
- `safety_keywords.hate_speech`
- `strategies.state`
- `conversation.keyword_acceleration.rules.ask_question.intent`
- `conversation.transitions.DOCUMENTING.modify`
- `message_reply_system.generators.empathy_generator.temperature`
- `domain_selection_mapping.number_mapping.一.domain_name`
- `message_templates.system.document.guidance`
- `message_reply_system.categories.error`
- `intent_system.intents.ask_question.action`
- `strategies.GLOBAL.ask_question.confused`
- `llm.models.openrouter-gemini-flash.provider`
- `domain_selection_mapping.business.suggestions.description_guidance`
- `llm.models.openrouter-gemini-flash`
- `message_templates.system.session.clear_domain_success`
- `business_rules.retry`
- `domain_selection_mapping.logging.debug.session_init_complete`
- `strategies.COLLECTING_INFO.provide_information.answer_question.positive.action`
- `message_templates.greeting.service_ready`
- `database.tables.focus_points.max_per_conversation`
- `message_reply_system.generators.chat_generator.description`
- `domain_selection_mapping.prompts.capabilities`
- `business_rules.retry.max_pending_attempts`
- `message_templates.clarification.request`
- `thresholds.limits.min_focus_points`
- `domain_selection_mapping.conversation.restart`
- `strategies.IDLE.business_requirement.neutral.action`
- `domain_selection_mapping.keyword_mapping.开发.domain_id`
- `intent_system.state_transitions.IDLE.domain_specific_query`
- `thresholds.quality.similarity_threshold`
- `strategies.GLOBAL.greeting`
- `intent_system.version`
- `intent_system.intents.process_query.priority`
- `message_reply_system.analytics.track_fallback_usage`
- `thresholds.limits.max_keywords`
- `integrations.external_apis`
- `domain_selection_mapping.business.suggestions.smart_tips.risk_management`
- `domain_selection_mapping.user_interaction.defaults.no_history`
- `knowledge_base.role_filters.allowed_roles`
- `strategy_templates.greeting_strategy.response_mapping.time_based.afternoon`
- `strategies.GLOBAL.ask_question.technical_question`
- `system.description`
- `thresholds.limits.session_max_duration`
- `database.queries.focus_points.get_status`
- `database.queries.admin_users.authenticate`
- `strategies.COLLECTING_INFO.skip.neutral.priority`
- `strategies.GLOBAL.business_requirement.design_requirement.neutral`
- `llm.scenario_params.optimized_question_generation.temperature`
- `message_reply_system.generators.greeting_generator`
- `strategies.COLLECTING_INFO.complete.neutral.prompt_instruction`
- `thresholds.performance.timeout.default`
- `intent_keywords.general_chat`
- `strategies.GLOBAL.provide_information.neutral.priority`
- `intent_system.state_transitions.IDLE.restart`
- `database.queries.documents`
- `security.content_moderation.masking.min_mask_length`
- `message_templates.system.document`
- `system_fallback_templates`
- `strategy_templates.greeting_strategy.response_mapping.emotion_based.neutral`
- `domain_selection_mapping.logging.error.load_focus_points_failed`
- `strategies.GLOBAL.reject.neutral.action`
- `strategies.COLLECTING_INFO.request_clarification.neutral.prompt_instruction`
- `strategies.IDLE.ask_question.requirement_question.neutral`
- `intent_system.state_transitions.COLLECTING_INFO.confirm`
- `conversation.states.available`
- `domain_selection_mapping.logging.warning.dynamic_reply_not_initialized`
- `strategies.GLOBAL.greeting.neutral.prompt_instruction`
- `llm.scenario_params.empathy_generator`
- `domain_selection_mapping.keyword_mapping.小程序.domain_name`
- `business_rules.quality_control.max_input_length`
- `strategies.GLOBAL.ask_question.technical_question.confused`
- `conversation.keyword_acceleration.rules.emotional_support.keywords`
- `message_templates.greeting.general_assistant`
- `emotion_keywords.positive`
- `domain_selection_mapping.logging.info.unknown_point`
- `performance.monitoring`
- `domain_selection_mapping.fallback.unknown_situation`
- `message_templates.system.processing.intent_detected`
- `strategies.DOCUMENTING.modify.neutral.priority`
- `thresholds.confidence.default`
- `llm.models.qwen-intent.top_p`
- `domain_selection_mapping.user_interaction.processing.llm_success_unknown`
- `security.rate_limiting.requests_per_minute`
- `strategies.GLOBAL.provide_information.anxious.action`
- `message_templates.error.timeout`
- `database.tables`
- `domain_selection_mapping.keyword_mapping.logo`
- `domain_selection_mapping.business.focus_points`
- `strategies.GLOBAL.restart.neutral`
- `llm.models.doubao-pro-32k.max_tokens`
- `system.logging.level`
- `domain_selection_mapping.number_mapping.3.domain_name`
- `security.rate_limiting`
- `domain_selection_mapping.keyword_mapping.设计`
- `strategies.GLOBAL.ask_question.anxious.priority`
- `business_rules.retry.max_total_attempts`
- `knowledge_base.chroma_db.collection_name`
- `security.content_moderation.actions.profanity`
- `intent_system.intents.restart`
- `llm.scenario_mapping.document_generator`
- `llm.models.deepseek-chat.max_retries`
- `message_reply_system.last_updated`
- `message_reply_system.categories.empathy.description`
- `llm.models.qwen-plus.model_name`
- `llm.models.openrouter-gemini-flash.max_retries`
- `llm.scenario_params.document_generator`
- `strategies.DOCUMENTING.confirm.positive.priority`
- `keyword_rules`
- `strategies.GLOBAL.unknown.anxious.action`
- `message_templates.greeting`
- `business_rules.action_handlers.handler_classes.KnowledgeBaseHandler`
- `domain_selection_mapping.logging.error.intent_processing_failed`
- `database.connection.check_same_thread`
- `domain_selection_mapping.formatting.status.unknown_focus_point`
- `llm.models.qwen-plus.max_tokens`
- `message_templates.clarification.need_more_info`
- `llm.scenario_params.structured_intent_classification.temperature`
- `domain_selection_mapping.capabilities.simple`
- `strategies.GLOBAL.ask_question.technical_question.neutral.action`
- `conversation.transitions.IDLE.greeting`
- `strategies.GLOBAL.ask_question.technical_question.neutral.priority`
- `intent_system.intents.restart.priority`
- `intent_system.state_transitions.IDLE.ask_question`
- `message_templates.system.processing.operation_failed`
- `performance.agent_cache.metrics_report_interval`
- `strategies.GLOBAL.complete.positive`
- `strategies.GLOBAL.business_requirement.marketing_requirement.positive.prompt_instruction`
- `domain_selection_mapping.keyword_mapping.知识产权.domain_name`
- `domain_selection_mapping.prompts.greeting`
- `message_templates.confirmation`
- `message_reply_system.generators.clarification_generator.instruction`
- `strategy_templates.requirement_strategy.templates`
- `strategies.COLLECTING_INFO.process_answer.neutral.action`
- `thresholds.cache_hit_ratio`
- `strategies.IDLE.ask_question.neutral.priority`
- `domain_selection_mapping.number_mapping.4`
- `domain_selection_mapping.logging.debug.problem_statement_restored`
- `strategies.GLOBAL.restart.neutral.priority`
- `system.decision_engine`
- `message_templates.guidance`
- `strategies.COLLECTING_INFO.request_clarification.confused`
- `llm.scenario_params.document_generation.temperature`
- `domain_selection_mapping.keyword_mapping.营销.domain_id`
- `strategies.DEFAULT_STRATEGY.prompt_instruction`
- `domain_selection_mapping.logging.debug.composite_intent_detected`
- `message_templates.greeting.basic`
- `domain_selection_mapping.business.focus_points.progress_awareness.half_complete`
- `domain_selection_mapping.exception.rephrase`
- `strategies.GLOBAL.provide_information.positive.priority`
- `strategies.COLLECTING_INFO.provide_information.positive.priority`
- `strategies.GLOBAL.provide_information`
- `domain_selection_mapping.keyword_mapping.设计.domain_name`
- `llm.scenario_params`
- `domain_selection_mapping.help.full`
- `domain_selection_mapping.prompts.introduction.instruction`
- `intent_keywords.ask_capabilities`
- `llm.scenario_params.apology_generator.temperature`
- `thresholds.strategy`
- `development.debug.log_requests`
- `domain_selection_mapping.empathy.fallback`
- `strategies.COLLECTING_INFO.reject.neutral`
- `domain_selection_mapping.capabilities`
- `strategy_templates.greeting_strategy.response_mapping.emotion_based.negative`
- `message_reply_system.supported_languages`
- `message_templates.system.session.restore_success`
- `database.queries.conversations.create_new`
- `domain_selection_mapping.capabilities.explanation`
- `security.session.timeout`
- `message_reply_system.categories.confirmation.fallback_template`
- `conversation.keyword_acceleration.rules.confirm.intent`
- `intent_system.state_transitions.DOCUMENTING.unknown`
- `intent_system.intents.domain_specific_query.examples`
- `domain_selection_mapping.capabilities.main`
- `security.content_moderation.actions.violence`
- `domain_selection_mapping.keyword_mapping.logo.domain_id`
- `message_templates.system.completed`
- `strategies.GLOBAL.restart`
- `strategies.IDLE.ask_question.neutral.action`
- `strategy_keywords.emotional_support_strategy`
- `thresholds.max_focus_points`
- `message_templates.system.session.reset_complete`
- `llm.scenario_params.domain_guidance_generator.max_tokens`
- `database.connection.timeout`
- `strategy_keywords.greeting_strategy.formal`
- `conversation.transitions.IDLE.business_requirement`
- `strategies.DOCUMENTING.general_request`
- `strategies.IDLE.ask_question.neutral`
- `business_rules.action_handlers.handler_classes`
- `domain_selection_mapping.introduction.youji_platform`
- `domain_selection_mapping.user_interaction.defaults.requirement_prompt`
- `thresholds.llm.default_max_tokens`
- `thresholds.llm.small_max_tokens`
- `llm.models.doubao-1.5-Lite.model_name`
- `intent_system.intents`
- `llm.scenario_params.intent_recognition`
- `message_templates.system.session.clear_messages_success`
- `strategies.GLOBAL.reject.neutral`
- `llm.scenario_params.clarification_generator.temperature`
- `knowledge_base.document_processing.chunk_overlap`
- `thresholds.max_message_length`
- `intent_system.intents.general_chat.action`
- `domain_selection_mapping.keyword_mapping.推广`
- `strategy_templates.knowledge_base_strategy.templates.response_templates.pricing`
- `strategy_templates.greeting_strategy.parameters.confidence_factors`
- `message_templates.error.safety.blocked_hate_speech`
- `conversation.keyword_acceleration.rules.confirm.keywords`
- `intent_system.intents.greeting.priority`
- `message_templates.clarification.detailed_clarification`
- `domain_selection_mapping.logging.error.init_collecting_state`
- `domain_selection_mapping.prompts.domain_guidance`
- `intent_system.intents.ask_introduction`
- `security.session.max_sessions_per_user`
- `performance.cache`
- `domain_selection_mapping.business.extraction.point_detail`
- `thresholds.limits.cache_max_size`
- `strategies.COLLECTING_INFO.process_answer.anxious.priority`
- `intent_system.intents.unknown.action`
- `knowledge_base.safety.rate_limit_per_minute`
- `performance.agent_cache.session_timeout_minutes`
- `domain_selection_mapping.exception.suggestions.fallback`
- `llm.scenario_params.default.provider`
- `strategies.GLOBAL.provide_information.positive.prompt_instruction`
- `conversation.keyword_acceleration.rules.general_chat.intent`
- `llm.scenario_params.optimized_question_generation`
- `thresholds.confidence`
- `strategies.GLOBAL.unknown.neutral.prompt_instruction`
- `emotion_keywords.negative`
- `domain_selection_mapping.logging.info.state_aware_processing`
- `intent_system.intents.emotional_support`
- `domain_selection_mapping.logging.info.composite_intent_resolution`
- `llm.scenario_params.information_extractor`
- `strategies.COLLECTING_INFO.request_clarification.anxious`
- `llm.scenario_params.document_generation.max_tokens`
- `message_templates.error.invalid_input`
- `intent_system.state_transitions.IDLE.business_requirement`
- `strategies.GLOBAL.request_clarification.question_clarification.anxious.prompt_instruction`
- `security.content_moderation.actions.sexual_content`
- `strategy_templates.greeting_strategy.response_mapping.time_based`
- `safety_keywords.jailbreak`
- `domain_selection_mapping.prompts.domain_guidance.instruction`
- `domain_selection_mapping.logging.info.domain_transition_documenting`
- `conversation.keyword_acceleration.rules.business_requirement`
- `strategies.IDLE.business_requirement`
- `llm.models.deepseek-chat.provider`
- `llm.models.doubao-pro-32k.api_key`
- `message_templates.message_reply_manager`
- `intent_system.intents.unknown.supported_states`
- `intent_system.intents.restart.examples`
- `database.queries.session_states`
- `strategies.GLOBAL.confirm.neutral.priority`
- `intent_system.intents.composite_knowledge_requirement.priority`
- `strategy_keywords.requirement_strategy`
- `thresholds.performance.timeout`
- `strategies.GLOBAL.ask_question.technical_question.neutral.prompt_instruction`
- `llm.models.qwen-turbo-latest.max_retries`
- `knowledge_base.enabled`
- `message_reply_system.categories.greeting.description`
- `strategies.DOCUMENTING.confirm`
- `message_reply_system.generators.introduction_generator.instruction`
- `message_templates.error.safety.warning_profanity`
- `domain_selection_mapping.conversation.modification.completed`
- `domain_selection_mapping.keyword_mapping.广告.domain_name`
- `domain_selection_mapping.keyword_mapping.软件.domain_id`
- `llm.models.qwen-turbo-latest.timeout`
- `business_rules.action_handlers.handler_classes.CompositeHandler`
- `strategies.GLOBAL.complete.positive.action`
- `llm.models.qwen-intent.temperature`
- `strategies.GLOBAL.ask_question.technical_question.neutral`
- `thresholds.performance.timeout.long`
- `thresholds.limits.max_query_length`
- `message_reply_system.language`
- `knowledge_base.features`
- `message_templates.system.document.generation_error`
- `intent_system.intents.general_chat.priority`
- `strategies.IDLE._state_config`
- `llm.scenario_mapping.domain_classifier`
- `strategies.GLOBAL.reject.negative.action`
- `thresholds.quality.spam_detection_threshold`
- `knowledge_base.features.intent_enhancement`
- `strategies.DOCUMENTING.reject.negative.priority`
- `strategies.DOCUMENTING.confirm.neutral.priority`
- `strategies.GLOBAL.request_clarification.term_clarification.neutral.prompt_instruction`
- `conversation.transitions.DOCUMENTING`
- `strategy_templates.emotional_support_strategy.templates.response_templates.positive.satisfaction`
- `knowledge_base.logging`
- `domain_selection_mapping.keyword_mapping.活动.domain_name`
- `strategies.GLOBAL.unknown.confused.priority`
- `message_templates.system.processing.message_received`
- `strategy_templates.greeting_strategy.parameters.confidence_factors.conversation_state.other`
- `strategies.GLOBAL.request_clarification.anxious.action`
- `strategy_keywords.knowledge_base_strategy`
- `strategies.COLLECTING_INFO.ask_question.neutral`
- `domain_selection_mapping.business.suggestions`
- `strategies.GLOBAL.reject.negative.priority`
- `database.queries.conversations`
- `strategies.COLLECTING_INFO.request_clarification.anxious.priority`
- `strategies.GLOBAL.unknown.neutral`
- `strategy_templates.greeting_strategy.parameters.confidence_factors.conversation_state.idle`
- `message_templates.error.message_processing`
- `system.decision_engine.enable_caching`
- `strategy_templates.emotional_support_strategy.templates.response_templates.tired`
- `performance.concurrency.queue_size`
- `strategies.COLLECTING_INFO.request_clarification.confused.action`
- `compatibility.legacy_mapping.product_info`
- `message_reply_system.categories.empathy`
- `knowledge_base.safety.max_query_length`
- `domain_selection_mapping.number_mapping.四.domain_id`
- `strategies.GLOBAL.business_requirement.software_development.neutral`
- `message_reply_system.generators.greeting_generator.instruction`
- `strategies.GLOBAL.request_clarification.term_clarification.neutral`
- `intent_system.decision_rules.default_state`
- `strategies.COLLECTING_INFO.provide_information.answer_question.anxious`
- `thresholds.max_retry_attempts`
- `domain_selection_mapping.number_mapping.5`
- `intent_system.intents.process_query.action`
- `message_templates.greeting.friendly`
- `intent_system.intents.composite_knowledge_requirement.supported_states`
- `strategies.GLOBAL.unknown.confused.action`
- `strategies.GLOBAL.complete.positive.priority`
- `message_templates.error.modification`
- `compatibility.legacy_mapping.registration`
- `message_reply_system.categories.empathy.priority`
- `strategies.GLOBAL.request_clarification.question_clarification.anxious.action`
- `domain_selection_mapping.keyword_mapping.ux.domain_name`
- `thresholds.performance.retry.quick_retry`
- `intent_system.intents.greeting.examples`
- `strategies.GLOBAL.business_requirement.marketing_requirement.positive`
- `intent_system.intents.search_knowledge_base.description`
- `message_templates.system.state.transition`
- `llm.models.doubao-pro-32k`
- `strategies.GLOBAL.request_clarification.term_clarification.confused.action`
- `llm.scenario_mapping.intent_recognition`
- `thresholds.performance.timeout.medium`
- `message_reply_system.generators.chat_generator.instruction`
- `strategies.GLOBAL.provide_information.anxious.prompt_instruction`
- `performance.agent_cache.memory_check_interval`
- `domain_selection_mapping.business.extraction`
- `strategies.GLOBAL.request_clarification.term_clarification.confused`
- `security.content_moderation.masking.keep_first_char`
- `llm.models.doubao-1.5-Lite.temperature`
- `strategies.GLOBAL.ask_question.requirement_question.anxious.prompt_instruction`
- `domain_selection_mapping.business.suggestions.single_point_simple`
- `domain_selection_mapping.logging.info.domain_category_saved`
- `intent_system.intents.ask_introduction.priority`
- `strategies.COLLECTING_INFO.process_answer.neutral.prompt_instruction`
- `strategies.GLOBAL.request_clarification.term_clarification.neutral.priority`
- `message_templates.error.safety.blocked_profanity`
- `strategies.error_handling`
- `message_reply_system.generators.chat_generator.enabled`
- `database.queries.documents.delete_document`
- `database.queries.conversations.get_active`
- `strategy_templates.greeting_strategy.response_mapping.time_based.evening`
- `llm.models.qwen-plus.provider`
- `security.input_validation.max_input_length`
- `llm.scenario_params.default.api_base`
- `llm.models.openrouter-gemini-flash.api_base`
- `domain_selection_mapping.keyword_mapping.交互.domain_name`
- `strategy_keywords.emotional_support_strategy.negative`
- `strategy_templates.fallback_strategy.templates.fallback_templates.general_help`
- `message_templates.system.action_executor.success`
- `strategies.COLLECTING_INFO.provide_information.positive.prompt_instruction`
- `llm.models.qwen-intent`
- `thresholds.business.document_quality_threshold`
- `strategies.DOCUMENTING.reject.negative`
- `knowledge_base.retrieval.similarity_threshold`
- `strategies.COLLECTING_INFO.provide_information.anxious.prompt_instruction`
- `intent_system.intents.process_query.supported_states`
- `message_reply_system.generators.introduction_generator.agent_name`
- `strategies.COLLECTING_INFO.request_clarification.anxious.prompt_instruction`
- `strategy_templates.greeting_strategy.greeting_type_detection.formal_indicators`
- `strategy_templates.knowledge_base_strategy.templates.response_templates.support`
- `intent_system.intents.request_clarification.description`
- `strategies.GLOBAL.business_requirement.marketing_requirement.positive.action`
- `knowledge_base.performance.cache_enabled`
- `intent_system.intents.ask_introduction.description`
- `domain_selection_mapping.number_mapping.二.domain_id`
- `domain_selection_mapping.conversation.modification.need_more_info`
- `integrations.knowledge_base`
- `message_templates.greeting.ai_assistant`
- `strategies.GLOBAL.ask_question.neutral.priority`
- `strategies.COLLECTING_INFO.reject.negative.priority`
- `domain_selection_mapping.requirement_collection`
- `domain_selection_mapping.logging.debug.domain_restore_attempt`
- `thresholds.confidence.decision_engine`
- `llm.scenario_mapping`
- `integrations.external_apis.openai`
- `llm.models.doubao-pro-32k.api_base`
- `message_templates.error.document_generation_failed`
- `llm.models.openrouter-gemini-flash.timeout`
- `intent_system.intents.ask_question.supported_states`
- `keyword_rules.general_chat`
- `llm.models.doubao-1.5-Lite.top_p`
- `message_templates.greeting.project_focused`
- `thresholds.limits.max_history_items`
- `message_templates.system.processing.special_state_logic`
- `message_templates.system.action_executor`
- `domain_selection_mapping.chat`
- `strategies.IDLE.business_requirement.anxious.prompt_instruction`
- `thresholds.limits`
- `message_reply_system.description`
- `thresholds.limits.max_concurrent`
- `strategies.GLOBAL.request_clarification.question_clarification`
- `message_reply_system.generators.capabilities_generator`
- `message_templates.error.general_unknown`
- `database.queries.documents.get_content`
- `business_rules.requirement_collection`
- `keyword_rules.restart`
- `message_templates.system.state.db_update_success`
- `business_rules.requirement_collection.max_focus_points`
- `domain_selection_mapping.keyword_mapping.营销.domain_name`
- `llm.scenario_params.document_generator.temperature`
- `strategies.DOCUMENTING.general_request.neutral`
- `database.queries.conversation_management`
- `message_reply_system.generators.chat_generator.temperature`
- `llm.default_params`
- `domain_selection_mapping.empathy`
- `domain_selection_mapping.user_interaction.defaults.unknown_intent`
- `domain_selection_mapping.requirement_collection.continue`
- `strategies.reply.personalization_enabled`
- `domain_selection_mapping.logging.warning.unrecognized_subtype`
- `message_templates.greeting.welcome`
- `domain_selection_mapping.user_interaction.redirect.business_needs`
- `intent_system.decision_rules.confidence_thresholds.high`
- `keyword_rules.greeting`
- `domain_selection_mapping.logging.info.state_transition_documenting`
- `message_reply_system.generators.clarification_generator.fallback_template`
- `domain_selection_mapping.keyword_mapping.广告.domain_id`
- `strategy_templates.greeting_strategy.parameters.confidence_factors.message_length`
- `thresholds.quality.max_input_length`
- `performance.agent_cache.enable_metrics`
- `thresholds.performance`
- `domain_selection_mapping.prompts`
- `domain_selection_mapping.keyword_mapping.知识产权.domain_id`
- `system_fallback_templates.system_initialization_fallback`
- `intent_system.intents.domain_specific_query`
- `thresholds.business.focus_point_priority_threshold`
- `llm.models.openrouter-gemini-flash.top_p`
- `message_reply_system.generators.clarification_generator.enabled`
- `database.tables.focus_points`
- `thresholds.security.sensitive_data_threshold`
- `llm.models.qwen-intent.model_name`
- `message_templates.system.processing.current_state_action`
- `strategies.COLLECTING_INFO.modify.neutral`
- `knowledge_base.chroma_db.path`
- `strategies.GLOBAL.greeting.positive`
- `domain_selection_mapping.number_mapping.五.domain_id`
- `intent_system.state_transitions.IDLE.request_clarification`
- `llm.scenario_params.default_generator.temperature`
- `strategy_templates.requirement_strategy.templates.collection_questions.design`
- `strategies.COLLECTING_INFO._state_config`
- `database.queries.focus_points.get_user_focus_points`
- `domain_selection_mapping.logging.info.reset_status`
- `message_templates.system.processing.general_decision_engine`
- `message_templates.error.emergency_fallback`
- `conversation.transitions.DOCUMENTING.confirm`
- `strategy_templates.emotional_support_strategy.templates.response_templates.negative`
- `intent_system.intents.domain_specific_query.supported_states`
- `performance.cache.max_size`
- `llm.scenario_params.default_generator`
- `thresholds.llm.default_timeout`
- `domain_selection_mapping.number_mapping.四.domain_name`
- `strategies.DEFAULT_STRATEGY.action`
- `strategies.GLOBAL.greeting.positive.action`
- `domain_selection_mapping.number_mapping.3`
- `database.queries.concern_point_coverage.get_processing_points`
- `thresholds.limits.max_focus_points`
- `strategy_templates.greeting_strategy.parameters.message_length_thresholds.short`
- `strategies.DOCUMENTING.reject`
- `llm.models.deepseek-chat.model_name`
- `intent_system.intents.request_clarification.priority`
- `strategies.COLLECTING_INFO.provide_information.neutral`
- `domain_selection_mapping.user_interaction`
- `database.queries.documents.check_exists`
- `message_templates.clarification.document_refinement`
- `thresholds.quality.abuse_detection_threshold`
- `strategies.COLLECTING_INFO.confirm.neutral.priority`
- `development.debug`
- `strategies.GLOBAL.business_requirement.marketing_requirement.neutral.priority`
- `message_templates.system.initialization`
- `security.input_validation.allowed_file_types`
- `domain_selection_mapping.logging.info.state_transition_collecting`
- `thresholds.quality.max_word_count`
- `intent_system.intents.process_query.description`
- `llm.scenario_params.greeting_generator.temperature`
- `domain_selection_mapping.keyword_mapping.界面`
- `strategies.COLLECTING_INFO.request_clarification.anxious.action`
- `database.queries.messages.get_conversation_history_limited`
- `strategies.GLOBAL.business_requirement.neutral`
- `strategies.COLLECTING_INFO.provide_information.confused`
- `message_reply_system.generators.empathy_generator.description`
- `knowledge_base_keywords.registration`
- `knowledge_base.safety`
- `conversation.keyword_acceleration.rules.greeting.intent`
- `domain_selection_mapping.business.suggestions.general_guidance`
- `message_reply_system.generators`
- `intent_system.decision_rules.confidence_thresholds.medium`
- `strategies.GLOBAL.business_requirement.marketing_requirement.neutral`
- `domain_selection_mapping.user_interaction.processing`
- `llm.scenario_params.clarification_generator.timeout`
- `domain_selection_mapping.user_interaction.processing.intent_string_not_json`
- `strategies.GLOBAL.unknown.anxious.priority`
- `strategies.COLLECTING_INFO.process_answer.neutral.priority`
- `security.content_moderation.logging.log_level`
- `thresholds.strategy.requirement`
- `knowledge_base.logging.log_queries`
- `thresholds.default_timeout`
- `domain_selection_mapping.conversation`
- `llm.models.qwen-max-latest`
- `strategies.COLLECTING_INFO.request_clarification.neutral.priority`
- `strategies.state.auto_save`
- `llm.scenario_params.structured_intent_classification`
- `strategies.DOCUMENTING.restart.neutral.action`
- `llm.scenario_params.structured_intent_classification.max_tokens`
- `intent_system.state_transitions.COLLECTING_INFO`
- `database.queries.documents.get_list`
- `strategies.GLOBAL.confirm.neutral.prompt_instruction`
- `strategy_templates.greeting_strategy.response_mapping.default`
- `strategies.COLLECTING_INFO.reject.neutral.action`
- `message_templates.conversation_flow_reply_mixin`
- `strategy_templates.capabilities_strategy.templates`
- `message_templates.error.safety.blocked_sexual_content`
- `thresholds.quality`
- `database.queries.documents.get_by_conversation`
- `strategies.GLOBAL.confirm.neutral`
- `intent_system.state_transitions.COLLECTING_INFO.process_answer`
- `security.content_moderation.actions.jailbreak`
- `strategies.COLLECTING_INFO.confirm.neutral`
- `intent_system.intents.confirm.action`
- `strategies.GLOBAL.provide_information.neutral`
- `database.queries.focus_points`
- `llm.models.qwen-intent.max_tokens`
- `business_rules.focus_point_priority`
- `strategies.COLLECTING_INFO.provide_information.anxious.priority`
- `strategy_templates.fallback_strategy.templates.fallback_templates.clarification`
- `thresholds.business.template_match_threshold`
- `strategies.IDLE.greeting`
- `thresholds.security.token_expiry`
- `llm.scenario_params.intent_recognition.max_tokens`
- `llm.scenario_params.information_extractor.max_tokens`
- `strategy_templates.fallback_strategy.templates.scenario_guides.project_inquiry`
- `intent_system.state_transitions.DOCUMENTING.confirm`
- `strategies.reply`
- `llm.models.doubao-pro-32k.top_p`
- `domain_selection_mapping.logging.info.extraction_result`
- `llm.scenario_params.document_generator.max_tokens`
- `strategies.IDLE.ask_question.requirement_question.neutral.priority`
- `strategies.GLOBAL.provide_information.confused`
- `intent_keywords.business_requirement`
- `system.performance.cache_enabled`
- `database.queries.max_results`
- `thresholds.performance.timeout.very_long`
- `message_templates.clarification`
- `system.logging.format`
- `llm.scenario_params.default`
- `knowledge_base.document_processing`
- `domain_selection_mapping.introduction.full`
- `intent_system.intents.system_capability_query.examples`
- `security.access_control`
- `business_rules.action_handlers.handler_classes.GeneralRequestHandler`
- `thresholds.security.rate_limit_per_minute`
- `strategies.IDLE.business_requirement.positive.priority`
- `llm.models.qwen-intent.timeout`
- `llm.models.qwen-plus.top_p`
- `llm.models.qwen-plus.max_retries`
- `message_reply_system.generators.default_generator.instruction`
- `security.session`
- `system.performance.llm_timeout`
- `message_templates.error.safety.warning_violence`
- `strategies.GLOBAL.greeting.neutral.action`
- `domain_selection_mapping.logging.error.unknown_situation_generation_failed`
- `strategies.DOCUMENTING.restart.neutral.prompt_instruction`
- `llm.scenario_params.default.max_retries`
- `database.queries.focus_points.clear_processing`
- `intent_system.intents.search_knowledge_base.supported_states`
- `integrations.knowledge_base.update_interval`
- `performance.agent_cache.max_memory_mb`
- `compatibility.legacy_mapping.support`
- `strategy_templates.requirement_strategy.templates.collection_questions.consulting`
- `domain_selection_mapping.keyword_mapping.法律.domain_name`
- `intent_system.intents.composite_knowledge_requirement.examples`
- `domain_selection_mapping.keyword_mapping.ux`
- `domain_selection_mapping.number_mapping.一.domain_id`
- `message_reply_system.generators.capabilities_generator.temperature`
- `thresholds.llm.low_temperature`
- `emotion_keywords.anxious`
- `llm.scenario_params.empathy_generator.max_tokens`
- `strategies.GLOBAL`
- `conversation.keyword_acceleration`
- `domain_selection_mapping`
- `strategies.DOCUMENTING.modify.neutral`
- `domain_selection_mapping.business.suggestions.smart_tips`
- `system_capability_keywords.greeting`
- `strategies.COLLECTING_INFO.confirm.neutral.action`
- `strategies.COLLECTING_INFO.ask_question.neutral.priority`
- `domain_selection_mapping.keyword_mapping.软件.domain_name`
- `domain_selection_mapping.logging.error`
- `domain_selection_mapping.formatting.history`
- `strategies.GLOBAL.business_requirement.marketing_requirement.positive.priority`
- `message_reply_system.a_b_testing.enabled`
- `thresholds.confidence.very_high`
- `strategies.GLOBAL.unknown`
- `intent_system.intents.system_capability_query.priority`
- `strategies.GLOBAL.unknown.confused`
- `strategies.DOCUMENTING._state_config.description`
- `message_templates.base_agent`
- `strategy_templates.greeting_strategy.parameters.confidence_factors.keyword_match`
- `message_reply_system.categories.guidance.fallback_template`
- `message_templates.clarification.general_request`
- `strategies.GLOBAL.business_requirement.anxious.priority`
- `intent_system.intents.process_answer`
- `domain_selection_mapping.prompts.empathy`
- `safety_keywords.pii_patterns`
- `strategies.GLOBAL.business_requirement.design_requirement.neutral.prompt_instruction`
- `conversation.transitions.DIRECT_SELECTION`
- `message_reply_system`
- `strategies.COLLECTING_INFO.provide_information.answer_question.anxious.prompt_instruction`
- `strategy_templates.knowledge_base_strategy.templates.response_templates.what_is`
- `message_templates.system`
- `strategies.GLOBAL.confirm.neutral.action`
- `strategies.DOCUMENTING.confirm.positive.prompt_instruction`
- `intent_system.intents.ask_question.description`
- `strategy_templates.emotional_support_strategy.templates.response_templates.negative.anger`
- `llm.models.qwen-intent.api_base`
- `message_reply_system.a_b_testing.test_groups.greeting.variant_a`
- `message_templates.greeting.enthusiastic`
- `strategies.COLLECTING_INFO.process_answer.anxious`
- `message_reply_system.a_b_testing.test_groups.greeting`
- `strategies.GLOBAL.reject.negative`
- `strategies.GLOBAL.business_requirement.software_development.neutral.action`
- `strategies.GLOBAL.business_requirement.positive.priority`
- `database.queries.focus_points.batch_insert`
- `strategy_templates.fallback_strategy.templates`
- `message_reply_system.generators.capabilities_generator.fallback_template`
- `llm.scenario_params.domain_classification.temperature`
- `strategies.COLLECTING_INFO.provide_information.answer_question.neutral.prompt_instruction`
- `strategies.DOCUMENTING.confirm.neutral.action`
- `strategies.GLOBAL.ask_question.technical_question.confused.priority`
- `message_reply_system.a_b_testing.test_groups.greeting.variant_b`
- `strategies.COLLECTING_INFO.ask_question`
- `message_reply_system.generators.chat_generator.agent_name`
- `strategies.COLLECTING_INFO.process_answer.neutral`
- `strategies.DOCUMENTING._state_config.fallback_intent`
- `domain_selection_mapping.business.question.user_context`
- `strategies.GLOBAL.ask_question.requirement_question.neutral`
- `knowledge_base.safety.enable_content_filter`
- `system.performance.cache_ttl`
- `strategies.GLOBAL.request_clarification`
- `strategies.state.session_timeout`
- `compatibility.legacy_mapping`
- `database.queries.messages.save_message`
- `strategies.GLOBAL.provide_information.positive.action`
- `llm.scenario_params.domain_guidance_generator.timeout`
- `strategies.DEFAULT_STRATEGY.priority`
- `thresholds.performance.retry.file_access`
- `message_templates.error.document_generation_not_initialized`
- `security.rate_limiting.requests_per_hour`
- `strategies.GLOBAL.reject`
- `domain_selection_mapping.keyword_mapping.活动`
- `database.queries.admin_logs`
- `llm.models.qwen-turbo-latest.api_key`
- `llm.scenario_params.domain_classifier.temperature`
- `domain_selection_mapping.introduction`
- `knowledge_base.features.rag_query`
- `strategies.COLLECTING_INFO.reject`
- `strategies.COLLECTING_INFO.confirm.neutral.prompt_instruction`
- `llm.scenario_params.default.max_tokens`
- `security.data_protection`
- `llm.scenario_params.domain_classification.max_tokens`
- `domain_selection_mapping.formatting.history.user_prefix`
- `database.queries.documents.find_by_conversation`
- `conversation.keyword_acceleration.rules.greeting.keywords`
- `system.use_structured_classification`
- `database.tables.documents`
- `llm.scenario_params.greeting_generator.timeout`
- `intent_keywords.search_knowledge_base`
- `performance.cache.enabled`
- `thresholds.performance.retry.database_operation`
- `thresholds.performance.timeout.database`
- `knowledge_base.retrieval.top_k`
- `domain_selection_mapping.number_mapping.五.domain_name`
- `domain_selection_mapping.keyword_mapping.app`
- `intent_system.state_transitions.IDLE.process_query`
- `domain_selection_mapping.number_mapping.5.domain_name`
- `strategy_templates.emotional_support_strategy.templates.response_templates.positive.gratitude`
- `thresholds.performance.timeout.llm_service`
- `domain_selection_mapping.formatting.history.ai_prefix`
- `strategies.GLOBAL.business_requirement.design_requirement`
- `domain_selection_mapping.keyword_mapping.小程序.domain_id`
- `conversation.transitions.CATEGORY_CLARIFICATION`
- `strategies.GLOBAL.restart.neutral.action`
- `system.performance.max_retry_attempts`
- `intent_system.description`
- `domain_selection_mapping.requirement_collection.start`
- `message_reply_system.generators.clarification_generator.description`
- `domain_selection_mapping.keyword_mapping.合同.domain_id`
- `message_reply_system.categories.completion.description`
- `llm.scenario_params.apology_generator.timeout`
- `database.queries.sessions`
- `message_reply_system.generators.greeting_generator.enabled`
- `domain_selection_mapping.exception.general_request.processing_error`
- `message_templates.error.general_fallback`
- `strategies.DEFAULT_STRATEGY`
- `business_rules.quality_control.min_input_length`
- `intent_system.state_transitions.IDLE.greeting`
- `keyword_rules.emotional_support`
- `strategies.GLOBAL.reject.neutral.prompt_instruction`
- `domain_selection_mapping.user_interaction.instructions`
- `intent_system.state_transitions.DOCUMENTING.modify`
- `intent_system.intents.composite_knowledge_requirement.action`
- `strategies.IDLE.ask_question.requirement_question.positive.action`
- `llm.scenario_params.llm_service.temperature`
- `domain_selection_mapping.number_mapping.3.domain_id`
- `domain_selection_mapping.exception.suggestions`
- `llm.models.doubao-1.5-Lite.api_base`
- `llm.scenario_mapping.empathy_generator`
- `message_templates.error.general_request_processing`
- `security.content_moderation.masking.replacement`
- `strategies.GLOBAL.ask_question.anxious`
- `intent_system.intents.business_requirement`
- `message_templates.system.processing`
- `domain_selection_mapping.empathy.negative_general`
- `strategy_templates.requirement_strategy.patterns`
- `intent_system.state_transitions.IDLE.feedback`
- `database.queries.focus_points.get_completed`
- `strategies.COLLECTING_INFO.request_clarification.neutral`
- `thresholds.limits.max_results`
- `intent_system.intents.feedback.supported_states`
- `domain_selection_mapping.business.focus_points.skip_continue`
- `domain_selection_mapping.logging.error.no_category_id`
- `message_templates.error.safety.self_harm_support`
- `intent_system.intents.modify.examples`
- `intent_system.decision_rules.priority_order`
- `message_templates.system.initialization.action_executor_success`
- `performance.concurrency.max_workers`
- `message_reply_system.a_b_testing.test_groups`
- `intent_system.intents.composite_knowledge_requirement`
- `strategies.COLLECTING_INFO.process_answer`
- `database.queries.statistics.count_distinct_users`
- `message_templates.clarification.problem_encountered`
- `strategy_templates.greeting_strategy.response_mapping`
- `strategies.COLLECTING_INFO._state_config.description`
- `message_reply_system.generators.default_generator.max_tokens`
- `knowledge_base.performance`
- `intent_system.state_transitions.IDLE.unknown`
- `conversation.keyword_acceleration.rules.general_chat.keywords`
- `message_templates.system.document.content_retrieval`
- `business_rules.action_handlers.handler_classes.RequirementHandler`
- `strategies.GLOBAL.business_requirement.marketing_requirement.neutral.prompt_instruction`
- `performance.cache.ttl`
- `thresholds.performance.retry.persistent_retry`
- `strategies.COLLECTING_INFO.complete.positive.prompt_instruction`
- `database.queries.batch_size`
- `domain_selection_mapping.requirement_collection.contextual_suggestions`
- `thresholds.response_time_limit`
- `strategies.GLOBAL.reset.neutral`
- `llm.scenario_params.information_extractor.timeout`
- `llm.models`
- `llm.scenario_params.conversation_flow.timeout`
- `database.queries.messages`
- `strategies.GLOBAL.business_requirement.neutral.action`
- `database.queries.conversations.get_by_user`
- `llm.models.deepseek-chat.api_key`
- `domain_selection_mapping.number_mapping.4.domain_name`
- `llm.models.qwen-max-latest.max_tokens`
- `strategies.DOCUMENTING.restart.neutral`
- `thresholds.llm.high_temperature`
- `llm.scenario_params.llm_service.max_tokens`
- `llm.scenario_mapping.clarification_generator`
- `thresholds.performance.timeout.file_operation`
- `domain_selection_mapping.fallback.general`
- `database.queries.conversations.delete_expired`
- `intent_system.intents.modify.priority`
- `strategies.COLLECTING_INFO.complete.positive.priority`
- `intent_system.intents.modify`
- `message_reply_system.generators.introduction_generator.description`
- `conversation.transitions.CATEGORY_CLARIFICATION.restart`
- `development.testing.mock_external_apis`
- `domain_selection_mapping.logging.error.initial_question_failed`
- `metadata.updated_date`
- `strategies.IDLE.greeting.neutral.prompt_instruction`
- `domain_selection_mapping.keyword_mapping.设计.domain_id`
- `strategies.GLOBAL.reset.neutral.prompt_instruction`
- `conversation.transitions.DOMAIN_CLARIFICATION.clarification_failed`
- `domain_selection_mapping.fallback.emergency`
- `llm.scenario_params.conversation_flow.temperature`
- `domain_selection_mapping.keyword_mapping.界面.domain_id`
- `strategies.COLLECTING_INFO.process_answer.anxious.prompt_instruction`
- `strategies.GLOBAL.provide_information.confused.action`
- `domain_selection_mapping.requirement_collection.contextual_suggestions.design_project`
- `domain_selection_mapping.business.suggestions.smart_tips.technical_choices`
- `strategies.GLOBAL.ask_question.requirement_question.neutral.prompt_instruction`
- `intent_system.intents.general_chat`
- `metadata.description`
- `strategies.IDLE.ask_question.requirement_question.neutral.prompt_instruction`
- `database.tables.conversations`
- `strategies.COLLECTING_INFO.provide_information.answer_question.neutral`
- `intent_system.intents.search_knowledge_base.action`
- `performance.concurrency`
- `intent_system.intents.process_answer.priority`
- `llm.scenario_mapping.apology_generator`
- `strategies.COLLECTING_INFO.provide_information.answer_question.neutral.action`
- `business_rules.document_confirmation`
